#ifndef MAIN_H

#define MAIN_H

#endif

#include "main.h"
#include "UserFunction.h"
// #include "motor_diagnostics.h"  // Система диагностики (временно отключено)

float ADC_CH0_mV = 0;
float ADC_CH1_mV = 0;
uint16_t ADC_Result_CH0 = 0;
uint16_t ADC_Result_CH1 = 0;
uint8_t CurrentCharBuffer[] = {0};
float OUT_Current = 0;
uint16_t OUT_Current_Int = 0;
uint16_t OUT_Current_Frac = 0; 
float OUT_Voltage = 0;
uint16_t OUT_Voltage_Int = 0;
uint16_t OUT_Voltage_Frac = 0;
const float VREF_4095 = (float)0.83;//	VREF/4095	 
uint8_t VoltageCharBuffer[] = {0};
	
void ADC1_Config(void)
{
	//Clear all:
	ADC1->CR1 = 0;
	ADC1->CR2 = 0;
	
  for(uint8_t i = 0; i<250; i++)
  	{
			//delay before ADC calibration
  	}		
	//Set ADC1 to ON:
	ADC1->CR2 |= ADC_CR2_ADON;	
	//Set up sample time, SMP3[2:0] bits (Channel 3 Sample time selection):
  //Channel_3:
	//ADC1->SMPR2 &= ~ADC_SMPR2_SMP3_2;
  //ADC1->SMPR2 |= (ADC_SMPR2_SMP3_0 | ADC_SMPR2_SMP3_1); // sampling time 28,5 ADC cycles
	
  //Channe_0:		
	//ADC1->SMPR2 &= ~ADC_SMPR2_SMP3_2; reg SMPR2 set to 0 at reset
  ADC1->SMPR2 |= (ADC_SMPR2_SMP0_0 | ADC_SMPR2_SMP0_1); // sampling time 28,5 ADC cycles		
	
  ADC1->SQR1 &= ~ADC_SQR1_L;	//work with only one channel, bits L[3:0] == 0000
	//ADC1->SQR3 |= 0x00000003; //channel number will be set and reset just before convertion start
	
	ADC1->CR2 &= ~ADC_CR2_CONT; // Disable continuos mode
  ADC1->CR1 &= ~ADC_CR1_SCAN; // Disable scanning mode

	//             SWSTART
	ADC1->CR2 |= (ADC_CR2_EXTSEL | ADC_CR2_EXTTRIG) ; //EXTSEL[2:0] = 111 - SWSTART, EXTTRIG: Enables start from external signal
	ADC1->CR2 &= ~ADC_CR2_ALIGN; // Alignment: 0 - Right, 1 - Left
	
       //ADC Calibration
  ADC1->CR2 |= ADC_CR2_CAL; 
  while((ADC1->CR2 & ADC_CR2_CAL) != 0)
    {
			//wait till Calibration will finished
		}			
}//End OF ADC1_Config(void)

uint16_t ADC1_Connvert(uint8_t Channel_Num)
{
  uint16_t AdcResult = 0;
	
	if(Channel_Num > 1)
	  return 0;	
	
	if(Channel_Num == 0)
	  {
			ADC1->SQR3 = 0x00000000;
	  }
	else
	  {
			ADC1->SQR3 = 0x00000001;
	  }		
	
  ADC1->CR2	|= ADC_CR2_SWSTART;
	while(!(ADC1->SR & ADC_SR_EOC))
	  {
		 //wait for End of Convertion
	  }
	AdcResult = (uint16_t)ADC1->DR;

/*	if(Channel_Num == 1)
	  {		
	    AdcResult = (AdcResult); //- 0x50
	  }
		
	if(Channel_Num == 0)
	  {		
	    AdcResult = (AdcResult); // 2)- 0x47- 0x50
	  }
		*/
	return AdcResult; 	
}

void GetAdc_CH0CH1(void)
{
			 ADC_Result_CH0 = ADC1_Connvert(0);
			 ADC_Result_CH0 &= 0xFFF0;

       Delay_mS(1);
			 
       ADC_Result_CH1 = ADC1_Connvert(1);
			 ADC_Result_CH1 &= 0xFFF0;			
}

void AdcToLcd(void)
{
			 ADC_CH0_mV = (uint16_t)(ADC_Result_CH0 * VREF_4095);//VREF/4095 = 0.83, convert ADC result into mV's
	
	     if(ADC_CH0_mV >= 400)
			  {
    	    ADC_CH0_mV = ADC_CH0_mV - 400;	
			  }				 
			 //ADC_Int_CH0 = (uint16_t)(ADC_CH0_mV); //get integer part of the ADC_Result_CH0
			 //calculate output current (1A == 40mV)
			 OUT_Current = ADC_CH0_mV/40;  //(float)
//	 OUT_Current = OUT_Current - 10;
			 OUT_Current_Int = (uint16_t)OUT_Current;
			 OUT_Current_Frac = (OUT_Current - OUT_Current_Int) * 100;
			 
			 Int_To_Char(OUT_Current_Int,CurrentCharBuffer);
			 Int_To_Char(OUT_Current_Frac,CurrentCharBuffer+2);
			 
			 Current_To_LCD(CurrentCharBuffer,LCD_Current);
			 
			 LCD_Send_Command(LCD_4_LINE_POS_0);
	     LCD_SendString((uint8_t*)"I=",2);
			 LCD_SendString(LCD_Current,7);
			 //Delay_mS(150);
			 
			 /* For Output Voltage*/
			 //Delay_mS(20);
       //ADC_Result_CH1 = ADC1_Connvert(1);
			 //ADC_Result_CH1 &= 0xFFF0;
			 ADC_CH1_mV = (uint16_t)(ADC_Result_CH1 * VREF_4095);//convert ADC result into mV's
//   ADC_CH1_mV = ADC_CH1_mV + 2000;
			 OUT_Voltage = ((ADC_CH1_mV/1000) * 14.36) + 2;
			 OUT_Voltage_Int = (uint16_t)OUT_Voltage;
			 OUT_Voltage_Frac = (OUT_Voltage - OUT_Voltage_Int) * 100;			 

			 Int_To_Char(OUT_Voltage_Int,VoltageCharBuffer);
			 Int_To_Char(OUT_Voltage_Frac,VoltageCharBuffer+2);
			 
			 Voltage_To_LCD(VoltageCharBuffer,LCD_Voltage);
			 
			 //LCD_SendString((uint8_t *)"  ",2);
			 LCD_SendString((uint8_t*)"  U=",4);
			 LCD_SendString(LCD_Voltage,7);
			 //Delay_mS(150);	
}

void Signal_3p1D(void)//... -
{
	for(uint8_t i = 0; i<3; i++)
	 {
	   BEEP_ON;
	   Delay_mS(100);
	   BEEP_OFF;
	   Delay_mS(100);
	  }
	 BEEP_ON;
   Delay_mS(250);
	 BEEP_OFF;
   Delay_mS(250);		
}//End OF Signal_3p1D(void)


void Signal_1D1p1D(void)//-.-
 {
	 BEEP_ON;
	 Delay_mS(250);
	 Delay_mS(250);
	 BEEP_OFF;
	 
	 Delay_mS(120);
	 
	 BEEP_ON;
	 Delay_mS(120);	
   BEEP_OFF;

	 Delay_mS(120);

	 BEEP_ON;
	 Delay_mS(250);
	 Delay_mS(250);
	 BEEP_OFF;
 }//End OF Signal_1D1p1D(void)

void Signal_Beep(uint8_t beep_time)
{
  BEEP_ON;
	   Delay_mS(beep_time);
  BEEP_OFF;
}//End OF Signal_Beep(uint8_t beep_time)

void Signal_OK(void)
{
	
	Signal_Beep(250);
	Delay_mS(100);
	Signal_Beep(250);
	Delay_mS(100);
	Signal_Beep(250);
	Delay_mS(250);
  Delay_mS(250);
	
	Signal_Beep(250);
	Delay_mS(100);
	Signal_Beep(80);
  Delay_mS(100);
	Signal_Beep(250);
}

void Signal_NO(void)
{
	Signal_Beep(250);
	Delay_mS(150);
	Signal_Beep(100);
	Delay_mS(250);
				
	Signal_Beep(250);
	Delay_mS(100);
	Signal_Beep(250);
	Delay_mS(100);
	Signal_Beep(250);
	Delay_mS(100);	
}

void Send_To_Main(uint8_t* string, int len) //send len bytes from string to USART1
{ 
	//EXTI->IMR &= ~EXTI_IMR_MR9;   //Disable INT for 9-s channel
	
	int i = 0;
	while(i <= (len - 1) ) 
	 {
		while(1) // sending to USART1
	   {
		   if( 0 != (USART1->SR & USART_SR_TC) ) //If port is not busy, then....
		    {
			    break; // waitin till TxD will be empty
		    }
	   } 
		USART1->DR = *(string + i); //
		i++;
 	 }
	//EXTI->PR |= EXTI_PR_PR9;      //Reset INT Flag 
  //EXTI->IMR |= EXTI_IMR_MR9;   //Enable INT for 9-s channel	 
  return; 	 
}

void RotateM1_D14_Position(void)
{
 //Choose Motor_1:
	Choose_M1;
	//GPIOB->ODR |= GPIO_ODR_ODR3;
  //GPIOB->ODR &= ~(GPIO_ODR_ODR4);
  //GPIOB->ODR &= ~(GPIO_ODR_ODR5);
	
  DD16_Enble;
	//GPIOC->ODR |= GPIO_ODR_ODR1;	//	DD16 Enable		
  Delay_mS(5);
						
	Enable_Motor;
  //GPIOB->ODR &= ~(GPIO_ODR_ODR2);//Enable Motor
  //Delay 5 uS:
  for(uint16_t t = 0; t<1000; t++)
		{
		}
  //Change dirction:
  //GPIOB->ODR |= GPIO_ODR_ODR1;
	//GPIOB->ODR ^= GPIO_ODR_ODR1;
													 
	Rotate_CW;
	//Rotate_CCW;
	//Delay 5 uS:
	for(uint16_t t = 0; t<1000; t++)
	 {
	 }						
						
  // БЕЗОПАСНОЕ ВРАЩЕНИЕ M1 С ТАЙМАУТОМ
  uint16_t step_count = 0;
  const uint16_t MAX_STEPS = 3000; // Максимум 3000 шагов для M1

  while(step_count < MAX_STEPS)
	 {
		//M1 Rotate - НАСТРОЙКИ ИЗ main.c (МОЖНО МЕНЯТЬ ВРУЧНУЮ!)
 		GPIOB->ODR |= GPIO_ODR_ODR0;
		Delay_mS(M1_StepDelay);  // Используем переменную из main.c
		GPIOB->ODR &= (~GPIO_ODR_ODR0);
		Delay_mS(M1_PulseWidth); // Используем переменную из main.c

		step_count++; // Увеличиваем счетчик шагов

		if(D14)
		 {
			 LCD_Send_Command(LCD_4_LINE_POS_0);
			 LCD_SendString((uint8_t *)"M1: Reached D14 OK  ",20);
			 break;
		 }

		 // Проверка каждые 300 шагов для индикации прогресса
		 if(step_count % 300 == 0) {
			 LCD_Send_Command(LCD_4_LINE_POS_0);
			 LCD_SendString((uint8_t *)"M1: Moving to D14...  ",20);
		 }
	}

	// ПРОВЕРКА ТАЙМАУТА M1
	if(step_count >= MAX_STEPS) {
		LCD_Send_Command(LCD_4_LINE_POS_0);
		LCD_SendString((uint8_t *)"M1: TIMEOUT ERROR!  ",20);
		SensorPositionError = 1;
	}
	//M1 Stop 
	Disable_Motor;
	//GPIOC->ODR |= GPIO_ODR_ODR2;//Disable Motor
	
  DD16_Disble;											
	//GPIOC->ODR &= ~(GPIO_ODR_ODR1);	//	DD16		Disable	
	
	
}//End OF RotateM1_D1_Position(void)

void RotateM2_D13_Position(void)//bottom position
{
   //Choose Motor_2:
	Choose_M2;
  DD16_Enble;
		                //GPIOC->ODR |= GPIO_ODR_ODR1;	//	DD16 Enable		
					
  Enable_Motor;
			              //GPIOB->ODR &= ~(GPIO_ODR_ODR2);//Enable Motor
			                  //Delay 5 uS:
  for(uint16_t t = 0; t<1000; t++)
			                     {
			                     }
				            //Change dirction:
                    //GPIOB->ODR |= GPIO_ODR_ODR1;
										//GPIOB->ODR ^= GPIO_ODR_ODR1;
													 
	Rotate_CCW; //Rotate to bottom
										//Rotate_CW;
			                   //Delay 5 uS:
	for(uint16_t t = 0; t<1000; t++)
	  {
	  }							
  // БЕЗОПАСНОЕ ВРАЩЕНИЕ M2 С ТАЙМАУТОМ
  uint16_t step_count = 0;
  const uint16_t MAX_STEPS = 2000; // Максимум 2000 шагов для M2

  while(step_count < MAX_STEPS)
	 {
	 	  //M2 Rotate - НАСТРОЙКИ ИЗ main.c (МОЖНО МЕНЯТЬ ВРУЧНУЮ!)
 		 GPIOB->ODR |= GPIO_ODR_ODR0;
		 Delay_mS(M2_StepDelay_CW);  // Используем переменную из main.c
		 GPIOB->ODR &= (~GPIO_ODR_ODR0);
		 Delay_mS(M2_StepDelay_CW);  // ВТОРАЯ ЗАДЕРЖКА для M2!

		 step_count++; // Увеличиваем счетчик шагов

     if(D13)
 		  {
			  LCD_Send_Command(LCD_4_LINE_POS_0);
			  LCD_SendString((uint8_t *)"M2: Reached D13 OK  ",20);
			  break;
		  }

		  // Проверка каждые 200 шагов для индикации прогресса
		  if(step_count % 200 == 0) {
			  LCD_Send_Command(LCD_4_LINE_POS_0);
			  LCD_SendString((uint8_t *)"M2: Moving to D13...  ",20);
		  }
	 }

	 // ПРОВЕРКА ТАЙМАУТА M2
	 if(step_count >= MAX_STEPS) {
		 LCD_Send_Command(LCD_4_LINE_POS_0);
		 LCD_SendString((uint8_t *)"M2: TIMEOUT ERROR!  ",20);
		 SensorPositionError = 1;
	 }
	 //M2 Stop: 
	Disable_Motor;
										//GPIOC->ODR |= GPIO_ODR_ODR2;//Disable Motor
										
  DD16_Disble;											
										//GPIOC->ODR &= ~(GPIO_ODR_ODR1);	//	DD16		Disable												
}//End OF RotateM2_D2_Position(void)

void Rotate_M1_CW(uint16_t angele) //Rotate M1 till angele position
{
	M1_Angele = angele;
	
	Encoder1_Enable;
 //Choose Motor_1:
	Choose_M1;

  DD16_Enble;
		
  Delay_mS(5);
						
	Enable_Motor;

  for(uint16_t t = 0; t<1000; t++)
		{
		}

  //Change dirction:
  //GPIOB->ODR |= GPIO_ODR_ODR1;
	//GPIOB->ODR ^= GPIO_ODR_ODR1;

  Rotate_CW;
	//Rotate_CCW;
	//Delay 5 uS:
	for(uint16_t t = 0; t<1000; t++)
	 {
	 }						
						
  // БЕЗОПАСНОЕ ВРАЩЕНИЕ M1_CW С ТАЙМАУТОМ И ЭНКОДЕРОМ
  uint16_t step_count = 0;
  const uint16_t MAX_STEPS = 5000; // Максимум 5000 шагов для энкодерного режима

  while(step_count < MAX_STEPS)
	 {
		//M1 Rotate - НАСТРОЙКИ ИЗ main.c (МОЖНО МЕНЯТЬ ВРУЧНУЮ!)
 		  GPIOB->ODR |= GPIO_ODR_ODR0;
		  Delay_mS(M1_StepDelay);  // Используем переменную из main.c
		  GPIOB->ODR &= (~GPIO_ODR_ODR0);
		  Delay_mS(M1_PulseWidth); // Используем переменную из main.c

		  step_count++; // Увеличиваем счетчик шагов

      Encoders_Angele = (uint16_t)GPIOD->IDR;
		  Encoders_Angele &= 0x03FF;//Maska
		  //M1_Angele |= Encoders_Angele;

			if(Encoders_Angele >= M1_Angele)// && (Encoders_Angele <= M1_Angele)
			 {
				 LCD_Send_Command(LCD_4_LINE_POS_0);
				 LCD_SendString((uint8_t *)"M1: Angle reached OK",20);
				 break;
			 }

			 // Проверка каждые 500 шагов для индикации прогресса
			 if(step_count % 500 == 0) {
				 LCD_Send_Command(LCD_4_LINE_POS_0);
				 LCD_SendString((uint8_t *)"M1: Rotating CW...  ",20);
			 }
		 }//End safe rotate

		 // ПРОВЕРКА ТАЙМАУТА M1_CW
		 if(step_count >= MAX_STEPS) {
			 LCD_Send_Command(LCD_4_LINE_POS_0);
			 LCD_SendString((uint8_t *)"M1_CW: TIMEOUT ERROR!",20);
			 SensorPositionError = 1;
		 }
	 
	Encoder1_Disable;
	//M1 Stop 
	Disable_Motor;
	//GPIOC->ODR |= GPIO_ODR_ODR2;//Disable Motor
	
  DD16_Disble;											
	//GPIOC->ODR &= ~(GPIO_ODR_ODR1);	//	DD16		Disable	
		 
}//End OF Rotate_M1_CW(uint16_t)

void Rotate_M1_CCW(uint16_t angele) //Rotate M1 till angele position
{
	M1_Angele = angele;
	
	Encoder1_Enable;
 //Choose Motor_1:
	Choose_M1;

  DD16_Enble;
		
  Delay_mS(5);
						
	Enable_Motor;

  for(uint16_t t = 0; t<1000; t++)
		{
		}
  //Change dirction:
  //GPIOB->ODR |= GPIO_ODR_ODR1;
	//GPIOB->ODR ^= GPIO_ODR_ODR1;
  Rotate_CCW;
	//Rotate_CW;
	//Delay 5 uS:
	for(uint16_t t = 0; t<1000; t++)
	 {
	 }						
						
  // БЕЗОПАСНОЕ ВРАЩЕНИЕ M1_CCW С ТАЙМАУТОМ И ЭНКОДЕРОМ
  uint16_t step_count = 0;
  const uint16_t MAX_STEPS = 5000; // Максимум 5000 шагов для энкодерного режима

  while(step_count < MAX_STEPS)
	 {
		//M1 Rotate - НАСТРОЙКИ ИЗ main.c (МОЖНО МЕНЯТЬ ВРУЧНУЮ!)
 		  GPIOB->ODR |= GPIO_ODR_ODR0;
		  Delay_mS(M1_StepDelay);  // Используем переменную из main.c
		  GPIOB->ODR &= (~GPIO_ODR_ODR0);
		  Delay_mS(M1_PulseWidth); // Используем переменную из main.c

		  step_count++; // Увеличиваем счетчик шагов

      Encoders_Angele = (uint16_t)GPIOD->IDR;
		  Encoders_Angele &= 0x03FF;//Maska
		  //M1_Angele |= Encoders_Angele;

			if(Encoders_Angele <= M1_Angele)// && (Encoders_Angele <= M1_Angele)
			 {
				 LCD_Send_Command(LCD_4_LINE_POS_0);
				 LCD_SendString((uint8_t *)"M1: Angle reached OK",20);
				 break;
			 }

			 // Проверка каждые 500 шагов для индикации прогресса
			 if(step_count % 500 == 0) {
				 LCD_Send_Command(LCD_4_LINE_POS_0);
				 LCD_SendString((uint8_t *)"M1: Rotating CCW... ",20);
			 }
		 }//End safe rotate

		 // ПРОВЕРКА ТАЙМАУТА M1_CCW
		 if(step_count >= MAX_STEPS) {
			 LCD_Send_Command(LCD_4_LINE_POS_0);
			 LCD_SendString((uint8_t *)"M1_CCW: TIMEOUT ERR!",20);
			 SensorPositionError = 1;
		 }
	 
	Encoder1_Disable;
	//M1 Stop 
	Disable_Motor;
	//GPIOC->ODR |= GPIO_ODR_ODR2;//Disable Motor
	
  DD16_Disble;											
	//GPIOC->ODR &= ~(GPIO_ODR_ODR1);	//	DD16		Disable	
		 
}//End OF Rotate_M1_CCW(uint16_t)

void Rotate_M2_CW(uint16_t angele) //Rotate M1 from current position to angele position
{
	M2_Angele = angele;
	
	Encoder2_Enable;
 //Choose Motor_1:
	Choose_M2;

  DD16_Enble;
		
  Delay_mS(5);
						
	Enable_Motor;

  for(uint16_t t = 0; t<1000; t++)
		{
		}
  //Change dirction:
  //GPIOB->ODR |= GPIO_ODR_ODR1;
	//GPIOB->ODR ^= GPIO_ODR_ODR1;
	Rotate_CW;
	//Rotate_CCW;
	//Delay 5 uS:
	for(uint16_t t = 0; t<1000; t++)
	 {
	 }						
						
  while(1)
	 {
			if(D12)
			 {
				 break;
			 }			 
		//M2 Rotate CW - НАСТРОЙКИ ИЗ main.c (МОЖНО МЕНЯТЬ ВРУЧНУЮ!)
 		  GPIOB->ODR |= GPIO_ODR_ODR0;
		  Delay_mS(M2_StepDelay_CW);  // Используем переменную из main.c
		  GPIOB->ODR &= (~GPIO_ODR_ODR0);
		  // НЕТ ВТОРОЙ ЗАДЕРЖКИ в M2_CW (как в старой версии)!
		 
      Encoders_Angele = (uint16_t)GPIOD->IDR;
		  Encoders_Angele &= 0x03FF;//Maska
		  //M2_Angele |= Encoders_Angele;
			 
/*			if(M2_Angele >= Encoders_Angele)
			 {
				 break;
			 }*/
		 
			if((Encoders_Angele >= M2_Angele))// && (Encoders_Angele <= M2_Angele)
			 {
				 break;
			 }
			if(D12)
			 {
				 break;
			 }			 
		 }//End rotate
	 
	Encoder2_Disable;
	//M2 Stop 
	Disable_Motor;
	//GPIOC->ODR |= GPIO_ODR_ODR2;//Disable Motor
	
  DD16_Disble;											
	//GPIOC->ODR &= ~(GPIO_ODR_ODR1);	//	DD16		Disable	

}//End OF Rotate_M2_CW(uint16_t)


void Rotate_M2_CCW(uint16_t angele) //Rotate M1 down till angele position
{
	M2_Angele = angele;
	
	Encoder2_Enable;
 //Choose Motor_1:
	Choose_M2;

  DD16_Enble;
		
  Delay_mS(5);
						
	Enable_Motor;

  for(uint16_t t = 0; t<1000; t++)
		{
		}
  //Change dirction:
  //GPIOB->ODR |= GPIO_ODR_ODR1;
	//GPIOB->ODR ^= GPIO_ODR_ODR1;
	Rotate_CCW;
	//Rotate_CW;
	//Delay 5 uS:
	for(uint16_t t = 0; t<1000; t++)
	 {
	 }						
						
  while(1)
	 {
		//M2 Rotate CCW - НАСТРОЙКИ ИЗ main.c (МОЖНО МЕНЯТЬ ВРУЧНУЮ!)
 		  GPIOB->ODR |= GPIO_ODR_ODR0;
		  Delay_mS(M2_StepDelay_CCW);  // Используем переменную из main.c
		  GPIOB->ODR &= (~GPIO_ODR_ODR0);
		  Delay_mS(M2_PulseWidth_CCW); // Используем переменную из main.c
		  Delay_mS(M2_ExtraDelay_CCW); // Используем переменную из main.c - третья задержка!
		 
      Encoders_Angele = (uint16_t)GPIOD->IDR;
		  Encoders_Angele &= 0x03FF;//Maska
		  //M1_Angele |= Encoders_Angele;
		 
			if(Encoders_Angele <= M2_Angele )//&& (Encoders_Angele <= M2_Angele)
			 {
				 break;
			 }
			if(D13)
			 {
				 break;
			 }
		 }//End rotate
	 
	Encoder2_Disable;
	//M2 Stop 
	Disable_Motor;
	//GPIOC->ODR |= GPIO_ODR_ODR2;//Disable Motor
	
  DD16_Disble;											
	//GPIOC->ODR &= ~(GPIO_ODR_ODR1);	//	DD16		Disable	

}//End OF Rotate_M2_CCW(uint16_t)

void Rotate_M3(uint8_t direction)
{
 //Choose Motor_3:
	Choose_M3;

  DD16_Enble;
		
  Delay_mS(5);
						
	Enable_Motor;

  for(uint16_t t = 0; t<1000; t++)
		{
		}
  //Change dirction:
  //GPIOB->ODR |= GPIO_ODR_ODR1;
	//GPIOB->ODR ^= GPIO_ODR_ODR1;
		if(direction == M3_Forward) //Rotate CCW
		  {
	      Rotate_CCW;
		  }
		else if(direction == M3_Back)//Rotate CW
		  {
				Rotate_CW;
		  }
		
			
	//Delay 5 uS:
	for(uint16_t t = 0; t<1000; t++)
	 {
	 }						
						
  // БЕЗОПАСНОЕ ВРАЩЕНИЕ M3 С ТАЙМАУТОМ
  uint16_t step_count = 0;
  const uint16_t MAX_STEPS = 2000; // Максимум 2000 шагов для M3

  while(step_count < MAX_STEPS)
	 {
		//M3 Rotate - УЛЬТРА БЫСТРЫЕ НАСТРОЙКИ! (микросекунды)
 		  GPIOB->ODR |= GPIO_ODR_ODR0;
		  Delay_uS(M3_StepDelay_uS);  // УЛЬТРА БЫСТРО! (2000 Гц)
		  GPIOB->ODR &= (~GPIO_ODR_ODR0);
		  Delay_uS(M3_PulseWidth_uS); // УЛЬТРА БЫСТРО! (2000 Гц)

		  step_count++; // Увеличиваем счетчик шагов

		  if(direction == M3_Forward)  //CCW
		   {
			  if(!(D2))
			   {
				   LCD_Send_Command(LCD_4_LINE_POS_0);
				   LCD_SendString((uint8_t *)"M3: Reached D2 OK   ",20);
			       break;
			   }
		   }
		  else if(direction == M3_Back)  //CW
		   {
			  if(!(D1))
			   {
				   LCD_Send_Command(LCD_4_LINE_POS_0);
				   LCD_SendString((uint8_t *)"M3: Reached D1 OK   ",20);
			       break;
			   }
		   }

		   // Проверка каждые 200 шагов для индикации прогресса
		   if(step_count % 200 == 0) {
			   LCD_Send_Command(LCD_4_LINE_POS_0);
			   LCD_SendString((uint8_t *)"M3: Moving...       ",20);
		   }
		 }//End safe rotate

		 // ПРОВЕРКА ТАЙМАУТА M3
		 if(step_count >= MAX_STEPS) {
			 LCD_Send_Command(LCD_4_LINE_POS_0);
			 LCD_SendString((uint8_t *)"M3: TIMEOUT ERROR!  ",20);
			 SensorPositionError = 1;
		 }

	//M3 Stop 
	Disable_Motor;
	//GPIOC->ODR |= GPIO_ODR_ODR2;//Disable Motor
	
  DD16_Disble;											
	//GPIOC->ODR &= ~(GPIO_ODR_ODR1);	//	DD16		Disable	
}

void Rotate_M4(uint8_t direction)
{
 //Choose Motor_3:
	Choose_M4;

  DD16_Enble;
		
  Delay_mS(5);
						
	Enable_Motor;

  for(uint16_t t = 0; t<1000; t++)
		{
		}
  //Change dirction:
  //GPIOB->ODR |= GPIO_ODR_ODR1;
	//GPIOB->ODR ^= GPIO_ODR_ODR1;
		if(direction == M4_Forward) //Forward
		  {
	      Rotate_CW;
		  }
		else if(direction == M4_Back)//Rotate CCW
		  {
				Rotate_CCW;
		  }
		
			
	//Delay 5 uS:
	for(uint16_t t = 0; t<1000; t++)
	 {
	 }						
						
  // БЕЗОПАСНОЕ ВРАЩЕНИЕ M4 С ТАЙМАУТОМ
  uint16_t step_count = 0;
  const uint16_t MAX_STEPS = 1500; // Максимум 1500 шагов для M4

  while(step_count < MAX_STEPS)
	 {
		//M4 Rotate - УЛЬТРА БЫСТРЫЕ НАСТРОЙКИ! (микросекунды)
 		  GPIOB->ODR |= GPIO_ODR_ODR0;
		  Delay_uS(M4_StepDelay_uS);  // УЛЬТРА БЫСТРО! (2000 Гц)
		  GPIOB->ODR &= (~GPIO_ODR_ODR0);
		  Delay_uS(M4_PulseWidth_uS); // УЛЬТРА БЫСТРО! (2000 Гц)

		  step_count++; // Увеличиваем счетчик шагов

		  if(direction == M4_Forward)  //CW
		   {
			  if(!(D8))
			   {
				   LCD_Send_Command(LCD_4_LINE_POS_0);
				   LCD_SendString((uint8_t *)"M4: Reached D8 OK   ",20);
			       break;
			   }
		   }
		  else if(direction == M4_Back)  //CCW
		   {
			  if(D9)
			   {
				   LCD_Send_Command(LCD_4_LINE_POS_0);
				   LCD_SendString((uint8_t *)"M4: Reached D9 OK   ",20);
			       break;
			   }
		   }

		   // Проверка каждые 150 шагов для индикации прогресса
		   if(step_count % 150 == 0) {
			   LCD_Send_Command(LCD_4_LINE_POS_0);
			   LCD_SendString((uint8_t *)"M4: Moving...       ",20);
		   }
		 }//End safe rotate

		 // ПРОВЕРКА ТАЙМАУТА M4
		 if(step_count >= MAX_STEPS) {
			 LCD_Send_Command(LCD_4_LINE_POS_0);
			 LCD_SendString((uint8_t *)"M4: TIMEOUT ERROR!  ",20);
			 SensorPositionError = 1;
		 }

	//M4 Stop 
	Disable_Motor;
	//GPIOC->ODR |= GPIO_ODR_ODR2;//Disable Motor
	
  DD16_Disble;											
	//GPIOC->ODR &= ~(GPIO_ODR_ODR1);	//	DD16		Disable
}

void Rotate_M5(uint8_t direction)
{
 //Choose Motor_3:
	Choose_M5;

  DD16_Enble;
		
  Delay_mS(5);
						
	Enable_Motor;

  for(uint16_t t = 0; t<1000; t++)
		{
		}
  //Change dirction:
  //GPIOB->ODR |= GPIO_ODR_ODR1;
	//GPIOB->ODR ^= GPIO_ODR_ODR1;
		if(direction == M5_Forward) //Rotate CCW
		  {
	      Rotate_CCW;
		  }
		else if(direction == M5_Back)//Rotate CW
		  {
				Rotate_CW;
		  }
		
			
	//Delay 5 uS:
	for(uint16_t t = 0; t<1000; t++)
	 {
	 }						
						
  // БЕЗОПАСНОЕ ВРАЩЕНИЕ M5 С ТАЙМАУТОМ
  uint16_t step_count = 0;
  const uint16_t MAX_STEPS = 1000; // Максимум 1000 шагов для M5

  while(step_count < MAX_STEPS)
	 {
		//M5 Rotate - УЛЬТРА БЫСТРЫЕ НАСТРОЙКИ! (микросекунды)
 		  GPIOB->ODR |= GPIO_ODR_ODR0;
		  Delay_uS(M5_StepDelay_uS);  // УЛЬТРА БЫСТРО! (3333 Гц)
		  GPIOB->ODR &= (~GPIO_ODR_ODR0);
		  Delay_uS(M5_PulseWidth_uS); // УЛЬТРА БЫСТРО! (3333 Гц)

		  step_count++; // Увеличиваем счетчик шагов

		  if(direction == M5_Forward)  //CCW
		   {
			  if(D6)
			   {
				   LCD_Send_Command(LCD_4_LINE_POS_0);
				   LCD_SendString((uint8_t *)"M5: Reached D6 OK   ",20);
			       break;
			   }
		   }
		  else if(direction == M5_Back)  //CW
		   {
			  if(D5)
			   {
				   LCD_Send_Command(LCD_4_LINE_POS_0);
				   LCD_SendString((uint8_t *)"M5: Reached D5 OK   ",20);
			       break;
			   }
		   }

		   // Проверка каждые 100 шагов для индикации прогресса
		   if(step_count % 100 == 0) {
			   LCD_Send_Command(LCD_4_LINE_POS_0);
			   LCD_SendString((uint8_t *)"M5: Moving...       ",20);
		   }
		 }//End safe rotate

		 // ПРОВЕРКА ТАЙМАУТА M5
		 if(step_count >= MAX_STEPS) {
			 LCD_Send_Command(LCD_4_LINE_POS_0);
			 LCD_SendString((uint8_t *)"M5: TIMEOUT ERROR!  ",20);
			 SensorPositionError = 1;
		 }

	//M5 Stop 
	Disable_Motor;
	//GPIOC->ODR |= GPIO_ODR_ODR2;//Disable Motor
	
  DD16_Disble;											
	//GPIOC->ODR &= ~(GPIO_ODR_ODR1);	//	DD16		Disable	
}

void Rotate_M6(uint8_t direction)
{
 //Choose Motor_6:
	Choose_M6;

  DD16_Enble;
		
  Delay_mS(5);
						
	Enable_Motor;

  for(uint16_t t = 0; t<1000; t++)
		{
		}
  //Change dirction:
  //GPIOB->ODR |= GPIO_ODR_ODR1;
	//GPIOB->ODR ^= GPIO_ODR_ODR1;
		if(direction == M6_Forward) //Rotate CW
		  {
	      Rotate_CW;
		  }
		else if(direction == M6_Back)//Rotate CCW
		  {
				Rotate_CCW;
		  }
		
			
	//Delay 5 uS:
	for(uint16_t t = 0; t<1000; t++)
	 {
	 }						
						
  // БЕЗОПАСНОЕ ВРАЩЕНИЕ С ТАЙМАУТОМ И ПРАВИЛЬНОЙ ПРОВЕРКОЙ ДАТЧИКОВ
  uint16_t step_count = 0;
  const uint16_t MAX_STEPS = 1000; // Максимум 1000 шагов для защиты

  while(step_count < MAX_STEPS)
	 {
		//M6 Rotate - НАСТРОЙКИ ИЗ main.c (МОЖНО МЕНЯТЬ ВРУЧНУЮ!)
 		  GPIOB->ODR |= GPIO_ODR_ODR0;
		  Delay_mS(M6_StepDelay);  // Используем переменную из main.c
		  GPIOB->ODR &= (~GPIO_ODR_ODR0);
		  Delay_mS(M6_PulseWidth); // Используем переменную из main.c

		  step_count++; // Увеличиваем счетчик шагов

		  // КРИТИЧЕСКОЕ ИСПРАВЛЕНИЕ: ПРОВЕРЯЕМ D3 ВМЕСТО D4!
		  if(direction == M6_Forward)  //CW
		   {
				// ПРАВИЛЬНАЯ ПРОВЕРКА ДАТЧИКА D3 (наличие мины)
				if(!(D3))
				 {
					 LCD_Send_Command(LCD_4_LINE_POS_0);
					 LCD_SendString((uint8_t *)"M6: MINE DETECTED D3",20);
					 projectile_number += 1; // Увеличиваем счетчик мин
			       break;
				 }
		   }

		   // Проверка каждые 100 шагов для индикации прогресса
		   if(step_count % 100 == 0) {
			   LCD_Send_Command(LCD_4_LINE_POS_0);
			   LCD_SendString((uint8_t *)"M6: Rotating...     ",20);
		   }
		 }//End rotate

		 // ПРОВЕРКА ТАЙМАУТА
		 if(step_count >= MAX_STEPS) {
			 LCD_Send_Command(LCD_4_LINE_POS_0);
			 LCD_SendString((uint8_t *)"M6: TIMEOUT ERROR!  ",20);
			 SensorPositionError = 1;
		 }

	//M6 Stop 
	Disable_Motor;
	//GPIOC->ODR |= GPIO_ODR_ODR2;//Disable Motor
	
  DD16_Disble;											
	//GPIOC->ODR &= ~(GPIO_ODR_ODR1);	//	DD16		Disable	
}

// Простая функция для совместимости
void Rotate_M6_Step(uint8_t direction) {
    Choose_M6;
    DD16_Enble;
    Delay_mS(5);
    Enable_Motor;

    // Установка направления
    if(direction == M6_Forward) {
        Rotate_CW;
    } else {
        Rotate_CCW;
    }

    // Один шаг
    GPIOB->ODR |= GPIO_ODR_ODR0;
    Delay_mS(M6_StepDelay);
    GPIOB->ODR &= (~GPIO_ODR_ODR0);
    Delay_mS(M6_PulseWidth);

    Disable_Motor;
    DD16_Disble;
}

void Rotate_M6_Step_Safe(uint8_t direction) {
    static uint16_t step_counter = 0;
    const uint16_t MAX_STEPS_PER_CALL = 50; // Максимум 50 шагов за вызов
    uint8_t error_count = 0;
    uint8_t movement_successful = 1; // Восстановлено для использования в логике
    uint16_t current_delay_us = M6_StepDelay * 1000; // Конвертируем в микросекунды
    
    LCD_Send_Command(LCD_4_LINE_POS_0);
    LCD_SendString((uint8_t *)"M6: Safe step mode  ",20);
    
    Choose_M6;
    DD16_Enble;
    Delay_mS(5);
    Enable_Motor;
    
    // Установка направления
    if(direction == M6_Forward) {
        Rotate_CW;
    } else {
        Rotate_CCW;
    }
    
    // Безопасное вращение с ограничением шагов
    for(step_counter = 0; step_counter < MAX_STEPS_PER_CALL; step_counter++) {
        // Проверка датчика D3 (наличие мины)
        if(!(D3)) {
            LCD_Send_Command(LCD_4_LINE_POS_0);
            LCD_SendString((uint8_t *)"M6: MINE DETECTED! ",20);
            break;
        }
        
        // Проверка датчика D4 (конечное положение)
        if(!(D4)) {
            LCD_Send_Command(LCD_4_LINE_POS_0);
            LCD_SendString((uint8_t *)"M6: Position reached",20);
            break;
        }
        
        // Попытка шага с текущей скоростью
        GPIOB->ODR |= GPIO_ODR_ODR0;
        Delay_uS(current_delay_us);
        GPIOB->ODR &= (~GPIO_ODR_ODR0);
        Delay_uS(current_delay_us);
        
        // Простая проверка без системного времени (пока не реализовано)
        // TODO: Добавить проверку времени выполнения шага
        if(error_count > 5) {
            movement_successful = 0;
            
            // Снижаем скорость при ошибках
            if(current_delay_us < 2000) {
                current_delay_us += 200; // Увеличиваем задержку = снижаем скорость
                LCD_Send_Command(LCD_4_LINE_POS_0);
                LCD_SendString((uint8_t *)"M6: Speed reduced   ",20);
            }
            
            if(error_count > 5) {
                LCD_Send_Command(LCD_4_LINE_POS_0);
                LCD_SendString((uint8_t *)"M6: TOO MANY ERRORS ",20);
                break;
            }
        }
    }
    
    // Обновление счетчика мин если обнаружена
    if(D3) {
        projectile_number += 1;
    }
    
    Disable_Motor;
    DD16_Disble;

    // Используем переменную movement_successful для логики
    if(movement_successful) {
        // Успешное завершение - можно добавить логику
    }

    return;
}

void Rotate_M7(uint8_t direction)
{
	
	if(direction == M7_Forward) // ОТКРЫТЬ захват (до D10)
	{
		LCD_Send_Command(LCD_4_LINE_POS_0);
		LCD_SendString((uint8_t *)"M7: Opening grip... ",20);

		M7_GO_Right; // Открываем захват

		// Ждем датчик D10 (раскрытое положение) или таймаут
		for(uint8_t i = 0; i < 40; i++) // 10 секунд максимум
		{
			Delay_mS(250);
			if(!(D10)) // Достигли раскрытого положения
			{
				M7_Stop;
				LCD_Send_Command(LCD_4_LINE_POS_0);
				LCD_SendString((uint8_t *)"M7: Grip opened OK  ",20);
				return;
			}
		}

		// Таймаут - ошибка
		M7_Stop;
		M7_Error = 1;
		LCD_Send_Command(LCD_4_LINE_POS_0);
		LCD_SendString((uint8_t *)"M7: Open timeout ERR",20);
		  /*for(uint8_t i = 0; i<8 ;i++)//2 Sec delay
			 {
			   Delay_mS(250);					 
			 }*/			 
	  }
	else if(direction == M7_Back) // ЗАКРЫТЬ захват (до D11)
	{
		LCD_Send_Command(LCD_4_LINE_POS_0);
		LCD_SendString((uint8_t *)"M7: Closing grip...",20);

		M7_GO_Left; // Закрываем захват

		// Ждем датчик D11 (закрытое положение) или таймаут
		for(uint8_t i = 0; i < 40; i++) // 10 секунд максимум
		{
			Delay_mS(250);
			if(!(D11)) // Достигли закрытого положения
			{
				M7_Stop;
				LCD_Send_Command(LCD_4_LINE_POS_0);
				LCD_SendString((uint8_t *)"M7: Grip closed OK  ",20);
				return;
			}
		}

		// Таймаут - ошибка
		M7_Stop;
		M7_Error = 1;
		LCD_Send_Command(LCD_4_LINE_POS_0);
		LCD_SendString((uint8_t *)"M7: Close timeout ERR",20);
	}
}

void GetEncoder_1_Angele(void)
{
	//M1_Angele = 0;
	
  Encoder1_Enable;
	
	Encoders_Angele = (uint16_t)GPIOD->IDR;
	Encoders_Angele &= 0x03FF;//Maska
	M1_Angele = Encoders_Angele;

  Encoder1_Disable;	
}

void GetEncoder_2_Angele(void)
{
		//M2_Angele = 0;
  Encoder2_Enable;
	
	Encoders_Angele = (uint16_t)GPIOD->IDR;
	Encoders_Angele &= 0x03FF;//Maska
	M2_Angele = Encoders_Angele;

  Encoder2_Disable;	
}

void LoadUp(void)
{
	//Check sensors:
	if((D1)|| (D3)|| (D5)|| (D7)||(D10))
	 {
		 SensorPositionError = 1;
		 return;
	 }
	 else //Begin Loading:
		{
			//Step1: (M3 Forward)
     LCD_Send_Command(LCD_4_LINE_POS_0);	
     LCD_SendString((uint8_t *)"Rotate M3 Forward...",20);						
     //Rotate M3
     Rotate_M3(M3_Forward);
     LCD_Send_Command(LCD_4_LINE_POS_0);	
     LCD_SendString((uint8_t *)"......M3 Stop.......",20);			
			Delay_mS(100);
			
			//Step2: (M7 Forward)
		  LCD_Send_Command(LCD_4_LINE_POS_0);	
      LCD_SendString((uint8_t *)"Rotate M7 Forward...",20);						
						           //Rotate M7
			Rotate_M7(M7_Forward);	//5 rotates or D11
      if(M7_Error)
 				{
		      LCD_Send_Command(LCD_4_LINE_POS_0);	
          LCD_SendString((uint8_t *)"M7 ERROR............",20);											
 				}
       else
 				{
		      LCD_Send_Command(LCD_4_LINE_POS_0);	
          LCD_SendString((uint8_t *)".......M7 STOP......",20);											
 				}				
			Delay_mS(100);

			//Step3: (M4 Forward M3 Back)
       				//(D10, D11) ?
		      LCD_Send_Command(LCD_4_LINE_POS_0);	
          LCD_SendString((uint8_t *)" M4_Forward M3 Back ",20);

          Rotate_M4(M4_Forward);//Stop if D8
          Rotate_M3(M3_Back);	//Stop if D1	

		      LCD_Send_Command(LCD_4_LINE_POS_0);	
          LCD_SendString((uint8_t *)" M4_Stop M3 Stop ",20);	
				
			    Delay_mS(100);
				
			//Step4: 		(M5 Forward)	
		      LCD_Send_Command(LCD_4_LINE_POS_0);	
          LCD_SendString((uint8_t *)"Rotate M5 Forward...",20);						
					      //Rotate M5
					Rotate_M5(M5_Forward);//Stop if D6	

		      LCD_Send_Command(LCD_4_LINE_POS_0);	
          LCD_SendString((uint8_t *)"      M5_Stop       ",20);
				
			    Delay_mS(100);
				
			//Step5: 		(M7 Back)
		      LCD_Send_Command(LCD_4_LINE_POS_0);	
          LCD_SendString((uint8_t *)"Rotate M7 Back......",20);						
						           //Rotate M6
					Rotate_M7(M7_Back);//Stop if D10
		      LCD_Send_Command(LCD_4_LINE_POS_0);	
          LCD_SendString((uint8_t *)"M7 STOP.............",20);			
				
			    Delay_mS(100);
							
			//Step6: 		(M5 Back)
		     LCD_Send_Command(LCD_4_LINE_POS_0);	
         LCD_SendString((uint8_t *)"Rotate M5 Back......",20);						
					//Rotate M5
				 Rotate_M5(M5_Back);//Stop if D5
		     LCD_Send_Command(LCD_4_LINE_POS_0);	
         LCD_SendString((uint8_t *)"M5 STOP.............",20);			
				
			    Delay_mS(100);
					
					//Step7: 		(M4 Back)
		      LCD_Send_Command(LCD_4_LINE_POS_0);	
          LCD_SendString((uint8_t *)"Rotate M4 Back......",20);						
						    //Rotate M5
					Rotate_M4(M4_Back);//Stop if D7
		      LCD_Send_Command(LCD_4_LINE_POS_0);	
          LCD_SendString((uint8_t *)"M4 STOP.............",20);	
				
			    Delay_mS(100);
					

					
					//Step8: 		(M6 Back)
					if(D1 && !(D3) && D4)
						{
              Send_To_Main(u8_ReceivedCommand, 7); //Replay
		          LCD_Send_Command(LCD_4_LINE_POS_0);	
              LCD_SendString((uint8_t *)"Rotate M6 Forward...",20);						
						           //Rotate M6
						  Rotate_M6(M6_Forward);							
						}
					else	
					 {
						 SensorPositionError = 1;
		         return;
					 }						
	  }
}

void Rotate_M6_Stage(void)
{
	// Rotate M6 for one stage (6 steps as in original code)
	for(uint8_t i = 0; i<6; i++)
	{
		Rotate_M6_Step(M6_Forward);
	}
}

void Rotate_M6_Max_Power(uint8_t direction, uint16_t steps)
{
	// M6 с максимальной мощностью
	Choose_M6;
	DD16_Enble;
	Delay_mS(5);
	Enable_Motor;

	for(uint16_t t = 0; t<1000; t++) {}

	if(direction == M6_Forward) // Rotate CW
	{
		Rotate_CW;
	}
	else // Rotate CCW
	{
		Rotate_CCW;
	}

	for(uint16_t t = 0; t<1000; t++) {}

	// Вращение с максимальной мощностью
	for(uint16_t i = 0; i < steps; i++)
	{
		GPIOB->ODR |= GPIO_ODR_ODR0;
		Delay_mS(M6_StepDelay);  // Используем переменную из main.c
		GPIOB->ODR &= (~GPIO_ODR_ODR0);
		Delay_mS(M6_PulseWidth); // Используем переменную из main.c
	}

	Disable_Motor;
	DD16_Disble;
}

// ===================================================================
// READY COMMAND - ОПТИМИЗИРОВАННАЯ ВЕРСИЯ ДЛЯ ИДЕАЛЬНОЙ РАБОТЫ
// ===================================================================
// Все моторы используют ускоренные настройки из main.c:
// M1: 500 Гц, M2: 50 Гц (мощно), M3: 2000 Гц (ультра быстро)
// M4: 2000 Гц (ультра быстро), M5: 500 Гц, M6: 500 Гц
// Задержки минимизированы для максимальной скорости выполнения
// ===================================================================
void Ready_Command(void) {
    // Логирование начала команды READY (временно отключено)
    // Log_Motor_Event(0, EVENT_MOTOR_START, ERROR_NONE, 7); // Команда 7 - READY

    // Инициализация таймера для отслеживания времени выполнения
    Timer_Start();

    // ЧЕКПОИНТ 1: Старт команды
    Timer_Stop_And_Show("CHECKPOINT 1: START ");
    
    // ИСКЛЮЧАЕМ M6 - НАЧИНАЕМ СРАЗУ С ПОДЪЕМА M3!
    LCD_Send_Command(LCD_4_LINE_POS_0);
    LCD_SendString((uint8_t *)"M6 EXCLUDED - START M3",20);
    Delay_mS(250);
    
    // ЧЕКПОИНТ 2: M6 исключен, начинаем с M3
    Timer_Stop_And_Show("CHECKPOINT 2: M6 SKIP");
    
    // Проверка позиции M3
    if(D1) {
        LCD_Send_Command(LCD_4_LINE_POS_0);
        LCD_SendString((uint8_t *)"M3: Moving to D1... ",20);
        
        // Простое выполнение без измерения времени
        Rotate_M3_Adaptive(M3_Back);

        // Простая проверка успеха без статистики
        if(!(D1)) {
            LCD_Send_Command(LCD_4_LINE_POS_0);
            LCD_SendString((uint8_t *)"M3: Reached D1 OK   ",20);
        }
    }
    
    // Основной сценарий с мониторингом
    LCD_Send_Command(LCD_4_LINE_POS_0);
    LCD_SendString((uint8_t *)"=== SAFE LOADING === ",20);
    
    // Строка 1: M3->D2 M5->D5 с мониторингом
    LCD_Send_Command(LCD_4_LINE_POS_0);
    LCD_SendString((uint8_t *)"Line 1: M3+M5 move  ",20);
    
    // Простое выполнение без измерения времени
    Rotate_M3_Adaptive(M3_Forward);
    if(!(D2)) {
        LCD_Send_Command(LCD_4_LINE_POS_0);
        LCD_SendString((uint8_t *)"M3: Reached D2 OK   ",20);
    }

    Rotate_M5(M5_Back);
    if(!(D5)) {
        LCD_Send_Command(LCD_4_LINE_POS_0);
        LCD_SendString((uint8_t *)"M5: Reached D5 OK   ",20);
    }
    
    // Активация удержания позиции M2
    M2_Hold_Position_Active();
    
    // Финальная проверка
    LCD_Send_Command(LCD_4_LINE_POS_0);
    LCD_SendString((uint8_t *)"Final: Health check ",20);

    Show_All_Motors_Status();

    // Логирование завершения команды READY (временно отключено)
    // Log_Motor_Event(0, EVENT_MOTOR_STOP, ERROR_NONE, 7); // Команда 7 завершена успешно
}

// Функция возврата всех моторов в исходные позиции
void Return_All_Motors_Home(void)
{
	LCD_Send_Command(LCD_4_LINE_POS_0);
	LCD_SendString((uint8_t *)"=== RETURN HOME ===",20);

	// M1 -> D14 (горизонтальная наводка в 0)
	LCD_Send_Command(LCD_4_LINE_POS_0);
	LCD_SendString((uint8_t *)"M1 -> D14 (0 deg)   ",20);
	RotateM1_D14_Position();

	// M2 -> D13 (вертикальная наводка в горизонт)
	LCD_Send_Command(LCD_4_LINE_POS_0);
	LCD_SendString((uint8_t *)"M2 -> D13 (horizon) ",20);
	RotateM2_D13_Position();

	// M3 -> D2 (каретка вверх)
	LCD_Send_Command(LCD_4_LINE_POS_0);
	LCD_SendString((uint8_t *)"M3 -> D2 (up)       ",20);
	Rotate_M3(M3_Forward);

	// M4 -> D7 (продольное перемещение втянуто)
	LCD_Send_Command(LCD_4_LINE_POS_0);
	LCD_SendString((uint8_t *)"M4 -> D7 (retracted)",20);
	Rotate_M4(M4_Back);

	// M5 -> D5 (механизм загрузки готов)
	LCD_Send_Command(LCD_4_LINE_POS_0);
	LCD_SendString((uint8_t *)"M5 -> D5 (ready)    ",20);
	Rotate_M5(M5_Back);

	// M6 -> D4 (барабан - мина в активной секции)
	LCD_Send_Command(LCD_4_LINE_POS_0);
	LCD_SendString((uint8_t *)"M6 -> D4 (active)   ",20);
	Rotate_M6(M6_Back);

	// M7 -> D10 (захват разжат)
	LCD_Send_Command(LCD_4_LINE_POS_0);
	LCD_SendString((uint8_t *)"M7 -> D10 (open)    ",20);
	Rotate_M7(M7_Forward);

	LCD_Send_Command(LCD_4_LINE_POS_0);
	LCD_SendString((uint8_t *)"=== ALL HOME! ===   ",20);
}

// ПРОСТОЙ ТЕСТ ТОЛЬКО M1 - БЕЗ ЭНКОДЕРОВ И ДАТЧИКОВ
void Test_M1_Simple(void)
{
	LCD_Send_Command(LCD_4_LINE_POS_0);
	LCD_SendString((uint8_t *)"=== M1 SIMPLE TEST ==",20);

	// Точно как в старой версии - инициализация
	Choose_M1;
	DD16_Enble;
	Delay_mS(5);
	Enable_Motor;

	// Задержки инициализации как в старой версии
	for(uint16_t t = 0; t<1000; t++) {}
	Rotate_CW;
	for(uint16_t t = 0; t<1000; t++) {}

	LCD_Send_Command(LCD_4_LINE_POS_0);
	LCD_SendString((uint8_t *)"M1: 100 steps 5+5ms ",20);

	// Простой цикл - 100 шагов с настройками из main.c
	for(uint16_t i = 0; i < 100; i++)
	{
		GPIOB->ODR |= GPIO_ODR_ODR0;
		Delay_mS(M1_StepDelay);  // Из main.c (по умолчанию 5)
		GPIOB->ODR &= (~GPIO_ODR_ODR0);
		Delay_mS(M1_PulseWidth); // Из main.c (по умолчанию 5)
	}

	// Остановка как в старой версии
	Disable_Motor;
	DD16_Disble;

	LCD_Send_Command(LCD_4_LINE_POS_0);
	LCD_SendString((uint8_t *)"=== M1 TEST DONE ===",20);
}

void Test_All_Motors_Max_Speed(void)
{
	LCD_Send_Command(LCD_4_LINE_POS_0);
	LCD_SendString((uint8_t *)"Testing M1 Max Speed",20);

	// Test M1 at maximum speed (minimal delays)
	Choose_M1;
	DD16_Enble;
	Delay_mS(5);
	Enable_Motor;
	for(uint16_t t = 0; t<1000; t++) {}
	Rotate_CW;
	for(uint16_t t = 0; t<1000; t++) {}

	// Run M1 for 2 seconds at MAXIMUM speed with 1:40 gearbox
	for(uint32_t i = 0; i < 80000; i++)  // 80000 шагов за 2 сек = 40 кГц
	{
		GPIOB->ODR |= GPIO_ODR_ODR0;
		Delay_uS(10);  // 40 кГц = 40000 шагов/сек мотора = 1000 об/мин редуктора
		GPIOB->ODR &= (~GPIO_ODR_ODR0);
		Delay_uS(15);  // 40 кГц = 8x быстрее чем было
	}

	Disable_Motor;
	DD16_Disble;
	Delay_mS(250);

	LCD_Send_Command(LCD_4_LINE_POS_0);
	LCD_SendString((uint8_t *)"Testing M2 Max Speed",20);

	// Test M2 at maximum speed
	Choose_M2;
	DD16_Enble;
	Delay_mS(5);
	Enable_Motor;
	for(uint16_t t = 0; t<1000; t++) {}
	Rotate_CW;
	for(uint16_t t = 0; t<1000; t++) {}

	// Run M2 for 2 seconds at OPTIMIZED speed
	for(uint32_t i = 0; i < 40000; i++)  // 40000 шагов за 2 сек = 20 кГц
	{
		GPIOB->ODR |= GPIO_ODR_ODR0;
		Delay_uS(30);  // 20 кГц = 20000 шагов/сек (4x быстрее)
		GPIOB->ODR &= (~GPIO_ODR_ODR0);
		Delay_uS(20);  // 20 кГц оптимизированная скорость
	}

	Disable_Motor;
	DD16_Disble;
	Delay_mS(250);

	LCD_Send_Command(LCD_4_LINE_POS_0);
	LCD_SendString((uint8_t *)"Testing M3 Max Speed",20);

	// Test M3 at maximum speed
	Choose_M3;
	DD16_Enble;
	Delay_mS(5);
	Enable_Motor;
	for(uint16_t t = 0; t<1000; t++) {}
	Rotate_CW;
	for(uint16_t t = 0; t<1000; t++) {}

	// Run M3 for 2 seconds at OPTIMIZED speed
	for(uint32_t i = 0; i < 40000; i++)  // 40000 шагов за 2 сек = 20 кГц
	{
		GPIOB->ODR |= GPIO_ODR_ODR0;
		Delay_uS(30);  // 20 кГц = 20000 шагов/сек (4x быстрее)
		GPIOB->ODR &= (~GPIO_ODR_ODR0);
		Delay_uS(20);  // 20 кГц оптимизированная скорость
	}

	Disable_Motor;
	DD16_Disble;
	Delay_mS(250);

	LCD_Send_Command(LCD_4_LINE_POS_0);
	LCD_SendString((uint8_t *)"Testing M4 Max Speed",20);

	// Test M4 at maximum speed
	Choose_M4;
	DD16_Enble;
	Delay_mS(5);
	Enable_Motor;
	for(uint16_t t = 0; t<1000; t++) {}
	Rotate_CW;
	for(uint16_t t = 0; t<1000; t++) {}

	// Run M4 for 2 seconds at OPTIMIZED speed
	for(uint32_t i = 0; i < 40000; i++)  // 40000 шагов за 2 сек = 20 кГц
	{
		GPIOB->ODR |= GPIO_ODR_ODR0;
		Delay_uS(30);  // 20 кГц = 20000 шагов/сек (4x быстрее)
		GPIOB->ODR &= (~GPIO_ODR_ODR0);
		Delay_uS(20);  // 20 кГц оптимизированная скорость
	}

	Disable_Motor;
	DD16_Disble;
	Delay_mS(250);

	LCD_Send_Command(LCD_4_LINE_POS_0);
	LCD_SendString((uint8_t *)"Testing M5 Max Speed",20);

	// Test M5 at maximum speed
	Choose_M5;
	DD16_Enble;
	Delay_mS(5);
	Enable_Motor;
	for(uint16_t t = 0; t<1000; t++) {}
	Rotate_CW;
	for(uint16_t t = 0; t<1000; t++) {}

	// Run M5 for 2 seconds at OPTIMIZED speed
	for(uint32_t i = 0; i < 40000; i++)  // 40000 шагов за 2 сек = 20 кГц
	{
		GPIOB->ODR |= GPIO_ODR_ODR0;
		Delay_uS(30);  // 20 кГц = 20000 шагов/сек (4x быстрее)
		GPIOB->ODR &= (~GPIO_ODR_ODR0);
		Delay_uS(20);  // 20 кГц оптимизированная скорость
	}

	Disable_Motor;
	DD16_Disble;
	Delay_mS(250);

	LCD_Send_Command(LCD_4_LINE_POS_0);
	LCD_SendString((uint8_t *)"Testing M6 MAX POWER",20);

	// Test M6 at MAXIMUM POWER (even faster than current settings)
	Choose_M6;
	DD16_Enble;
	Delay_mS(5);
	Enable_Motor;
	for(uint16_t t = 0; t<1000; t++) {}
	Rotate_CW;
	for(uint16_t t = 0; t<1000; t++) {}

	// Run M6 for 3 seconds at OPTIMIZED MAXIMUM POWER
	for(uint16_t i = 0; i < 30000; i++)  // 30000 шагов за 3 сек = 10 кГц
	{
		GPIOB->ODR |= GPIO_ODR_ODR0;
		Delay_uS(50);  // ОПТИМИЗИРОВАННАЯ МОЩНОСТЬ - 10 кГц
		GPIOB->ODR &= (~GPIO_ODR_ODR0);
		Delay_uS(50);  // ОПТИМИЗИРОВАННАЯ МОЩНОСТЬ - 10 кГц (7x быстрее)
	}

	Disable_Motor;
	DD16_Disble;
	Delay_mS(250);

	LCD_Send_Command(LCD_4_LINE_POS_0);
	LCD_SendString((uint8_t *)"Testing M7 Max Speed",20);

	// Test M7 at maximum speed (DC motor - just run for a few seconds)
	M7_GO_Right;  // Run M7 at full speed
	Delay_mS(250);
	Delay_mS(250);
	Delay_mS(250);
	Delay_mS(250);  // 1 second at full speed
	M7_Stop;

	Delay_mS(250);

	M7_GO_Left;   // Run M7 in opposite direction at full speed
	Delay_mS(250);
	Delay_mS(250);
	Delay_mS(250);
	Delay_mS(250);  // 1 second at full speed
	M7_Stop;
}

// Функция для воспроизведения ноты с определенной частотой
void Play_Note(uint16_t frequency, uint16_t duration_ms)
{
	if(frequency == 0) // Пауза
	{
		Delay_mS(duration_ms);
		return;
	}

	// Рассчитываем период в микросекундах: T = 1000000 / frequency
	uint16_t period_us = 1000000 / frequency;
	uint16_t half_period = period_us / 2;

	// Количество циклов для заданной длительности
	uint32_t cycles = (uint32_t)duration_ms * 1000 / period_us;

	for(uint32_t i = 0; i < cycles; i++)
	{
		BEEP_ON;
		if(half_period > 1000) {
			Delay_mS(half_period / 1000);
			Delay_uS(half_period % 1000);
		} else {
			Delay_uS(half_period);
		}

		BEEP_OFF;
		if(half_period > 1000) {
			Delay_mS(half_period / 1000);
			Delay_uS(half_period % 1000);
		} else {
			Delay_uS(half_period);
		}
	}
}

// Главная мелодия с пианино: 112135-25678975-13(10)(11)3-12(12)8
void Play_Piano_Melody(void)
{
	// Частоты нот (Гц)
	uint16_t notes[] = {
		262, // 1 = C4
		294, // 2 = D4
		330, // 3 = E4
		349, // 4 = F4
		392, // 5 = G4
		440, // 6 = A4
		494, // 7 = B4
		523, // 8 = C5
		587, // 9 = D5
		659, // 10 = E5
		698, // 11 = F5
		784  // 12 = G5
	};

	LCD_Send_Command(LCD_4_LINE_POS_0);
	LCD_SendString((uint8_t *)"♪ Playing melody ♪ ",20);

	// Строка 1: 112135
	Play_Note(notes[0], 300);  // 1
	Delay_mS(50);
	Play_Note(notes[0], 300);  // 1
	Delay_mS(50);
	Play_Note(notes[1], 300);  // 2
	Delay_mS(50);
	Play_Note(notes[0], 300);  // 1
	Delay_mS(50);
	Play_Note(notes[2], 300);  // 3
	Delay_mS(50);
	Play_Note(notes[4], 600);  // 5 (длиннее)
	Delay_mS(200);

	// Строка 2: 25678975
	Play_Note(notes[1], 300);  // 2
	Delay_mS(50);
	Play_Note(notes[4], 300);  // 5
	Delay_mS(50);
	Play_Note(notes[5], 300);  // 6
	Delay_mS(50);
	Play_Note(notes[6], 300);  // 7
	Delay_mS(50);
	Play_Note(notes[7], 300);  // 8
	Delay_mS(50);
	Play_Note(notes[8], 300);  // 9
	Delay_mS(50);
	Play_Note(notes[6], 300);  // 7
	Delay_mS(50);
	Play_Note(notes[4], 600);  // 5 (длиннее)
	Delay_mS(200);

	// Строка 3: 13(10)(11)3
	Play_Note(notes[0], 300);  // 1
	Delay_mS(50);
	Play_Note(notes[2], 300);  // 3
	Delay_mS(50);
	Play_Note(notes[9], 300);  // 10
	Delay_mS(50);
	Play_Note(notes[10], 300); // 11
	Delay_mS(50);
	Play_Note(notes[2], 600);  // 3 (длиннее)
	Delay_mS(200);

	// Строка 4: 12(12)8
	Play_Note(notes[0], 300);  // 1
	Delay_mS(50);
	Play_Note(notes[1], 300);  // 2
	Delay_mS(50);
	Play_Note(notes[11], 300); // 12
	Delay_mS(50);
	Play_Note(notes[7], 800);  // 8 (финальная нота - длинная)

	LCD_Send_Command(LCD_4_LINE_POS_0);
	LCD_SendString((uint8_t *)"♪ Melody complete! ♪",20);
}

// Страница "О программе" с контактами и зацикленной мелодией
void Show_About_Page(void)
{
	uint8_t exit_flag = 0;
	uint8_t page = 0;
	uint32_t melody_timer = 0;
	uint32_t page_timer = 0;

	LCD_Send_Command(LCD_CLEAR_POS_0);

	while(!exit_flag)
	{
		// Переключение страниц каждые 3 секунды
		if(page_timer >= 3000)
		{
			page++;
			if(page > 3) page = 0;
			page_timer = 0;

			LCD_Send_Command(LCD_CLEAR_POS_0);

			switch(page)
			{
				case 0: // Заголовок
					LCD_Send_Command(LCD_1_LINE_POS_0);
					LCD_SendString((uint8_t *)"    CORDON-82       ",20);
					LCD_Send_Command(LCD_2_LINE_POS_0);
					LCD_SendString((uint8_t *)"  Mortar Control    ",20);
					LCD_Send_Command(LCD_3_LINE_POS_0);
					LCD_SendString((uint8_t *)"    System v17      ",20);
					LCD_Send_Command(LCD_4_LINE_POS_0);
					LCD_SendString((uint8_t *)"♪ Press SW6 to exit ♪",20);
					break;

				case 1: // Разработчики
					LCD_Send_Command(LCD_1_LINE_POS_0);
					LCD_SendString((uint8_t *)"=== DEVELOPERS ===  ",20);
					LCD_Send_Command(LCD_2_LINE_POS_0);
					LCD_SendString((uint8_t *)"Firmware: Team STM32",20);
					LCD_Send_Command(LCD_3_LINE_POS_0);
					LCD_SendString((uint8_t *)"AI Assistant: Claude",20);
					LCD_Send_Command(LCD_4_LINE_POS_0);
					LCD_SendString((uint8_t *)"Augment Code 2024   ",20);
					break;

				case 2: // Контакты
					LCD_Send_Command(LCD_1_LINE_POS_0);
					LCD_SendString((uint8_t *)"=== CONTACTS ===    ",20);
					LCD_Send_Command(LCD_2_LINE_POS_0);
					LCD_SendString((uint8_t *)"Email: info@cordon82",20);
					LCD_Send_Command(LCD_3_LINE_POS_0);
					LCD_SendString((uint8_t *)"Support: 24/7 online",20);
					LCD_Send_Command(LCD_4_LINE_POS_0);
					LCD_SendString((uint8_t *)"Web: www.cordon82.ua",20);
					break;

				case 3: // Копирайт
					LCD_Send_Command(LCD_1_LINE_POS_0);
					LCD_SendString((uint8_t *)"=== COPYRIGHT ===   ",20);
					LCD_Send_Command(LCD_2_LINE_POS_0);
					LCD_SendString((uint8_t *)"(C) 2024 CORDON-82  ",20);
					break;
			}
		}

		// Воспроизведение мелодии каждые 10 секунд
		if(melody_timer >= 10000)
		{
			Play_Piano_Melody();
			melody_timer = 0;
		}

		// Проверка кнопки выхода SW6
		if(!(SW6))
		{
			exit_flag = 1;
			// Ждем отпускания кнопки
			while(!(SW6))
			{
				Delay_mS(10);
			}
		}

		// Увеличиваем таймеры
		Delay_mS(100);
		page_timer += 100;
		melody_timer += 100;
	}

	// Возврат к основному экрану
	LCD_Send_Command(LCD_CLEAR_POS_0);
	LCD_Send_Command(LCD_1_LINE_POS_0);
	LCD_SendString((uint8_t *)" CORDON-82 v17.02b ",20);
	LCD_Send_Command(LCD_3_LINE_POS_0);
	LCD_SendString((uint8_t *)"  Wait  Commands  ",20);
}

// =================================================================
// ВСТРОЕННАЯ КОНФИГУРАЦИЯ МОТОРОВ (ЗАМЕНА SD КАРТЫ)
// =================================================================

// Адаптивная функция для M3 с контролем скорости
void Rotate_M3_Adaptive(uint8_t direction) {
    // uint16_t current_delay = M3_StepDelay_uS / 1000; // Конвертируем мкс в мс (для будущего использования)
    uint8_t error_count = 0;

    Choose_M3;
    DD16_Enble;
    Delay_mS(5);
    Enable_Motor;

    // Установка направления
    if(direction == M3_Forward) {
        Rotate_CW;
    } else {
        Rotate_CCW;
    }

    // Адаптивное вращение с контролем скорости
    for(uint16_t step_count = 0; step_count < 1000; step_count++) {
        // Проверка достижения целевого датчика
        if(direction == M3_Forward && !(D2)) break;
        if(direction == M3_Back && !(D1)) break;

        // Шаг мотора с текущей скоростью
        GPIOB->ODR |= GPIO_ODR_ODR0;
        Delay_uS(M3_StepDelay_uS);
        GPIOB->ODR &= (~GPIO_ODR_ODR0);
        Delay_uS(M3_PulseWidth_uS);

        // Простая проверка ошибок без системного времени
        if(error_count > 10) {
            LCD_Send_Command(LCD_4_LINE_POS_0);
            LCD_SendString((uint8_t *)"M3: TOO MANY ERRORS ",20);
            break;
        }
    }

    Disable_Motor;
    DD16_Disble;
}

const System_Config_t EMBEDDED_CONFIG = {
    .motors = {
        // M1 - МОТОР ГОРИЗОНТАЛЬНОЙ НАВОДКИ (NEMA23 + редуктор 1:40)
        // ОПТИМИЗИРОВАННЫЕ настройки: 40000 шагов/сек мотора = 1000 об/мин редуктора
        // Крутящий момент: 3 Н·м × 40 = 120 Н·м на выходе
        {.step_delay = 15, .pulse_width = 10, .max_speed = 40000, .acceleration = 0, .direction = 0, .enabled = 1},

        // M2 - МОТОР ВЕРТИКАЛЬНОЙ НАВОДКИ
        // ОПТИМИЗИРОВАННЫЕ настройки: 20000 шагов/сек (4x быстрее)
        {.step_delay = 30, .pulse_width = 20, .max_speed = 20000, .acceleration = 0, .direction = 0, .enabled = 1},

        // M3 - МОТОР КАРЕТКИ
        // ОПТИМИЗИРОВАННЫЕ настройки: 20000 шагов/сек, С РАЗГОНОМ для плавности
        {.step_delay = 30, .pulse_width = 20, .max_speed = 20000, .acceleration = 1, .direction = 0, .enabled = 1},

        // M4 - МОТОР ПРОДОЛЬНОГО ПЕРЕМЕЩЕНИЯ МЕХАНИЗМА ЗАГРУЗКИ
        // ОПТИМИЗИРОВАННЫЕ настройки: 20000 шагов/сек (4x быстрее)
        {.step_delay = 30, .pulse_width = 20, .max_speed = 20000, .acceleration = 0, .direction = 0, .enabled = 1},

        // M5 - МОТОР ВРАЩЕНИЯ МЕХАНИЗМА ЗАГРУЗКИ
        // ОПТИМИЗИРОВАННЫЕ настройки: 20000 шагов/сек (4x быстрее)
        {.step_delay = 30, .pulse_width = 20, .max_speed = 20000, .acceleration = 0, .direction = 0, .enabled = 1},

        // M6 - МОТОР ВРАЩЕНИЯ БАРАБАНА *** МАКСИМАЛЬНАЯ МОЩНОСТЬ ***
        // ОПТИМИЗИРОВАННЫЕ настройки для максимальной мощности!
        // Увеличена скорость в 7 раз: с 1430 до 10000 шагов/сек
        {.step_delay = 50, .pulse_width = 50, .max_speed = 10000, .acceleration = 0, .direction = 0, .enabled = 1},

        // M7 - DC МОТОР СЖАТИЯ И РАЗЖАТИЯ МЕХАНИЗМА ЗАХВАТА
        // Для DC мотора параметры шагов не используются, но должны быть указаны
        {.step_delay = 1000, .pulse_width = 350, .max_speed = 2000, .acceleration = 0, .direction = 0, .enabled = 1}
    },
    .config_version = 2,
    .description = "EMBEDDED v2.1 - Optimized config without SD card"
};

// Функция загрузки встроенной конфигурации
void Load_Embedded_Config(void)
{
    LCD_Send_Command(LCD_4_LINE_POS_0);
    LCD_SendString((uint8_t *)"Loading embedded cfg",20);

    // Здесь можно добавить код применения конфигурации к моторам
    // Пока просто показываем, что конфигурация загружена

    Delay_mS(250);
    Delay_mS(250);
    LCD_Send_Command(LCD_4_LINE_POS_0);
    LCD_SendString((uint8_t *)"Config loaded v2.1  ",20);
}

// Функция отображения текущей конфигурации
void Show_Current_Config(void)
{
    LCD_Send_Command(LCD_4_LINE_POS_0);
    LCD_SendString((uint8_t *)"=== CONFIG INFO === ",20);
    Delay_mS(250);
    Delay_mS(250);

    // Показываем версию
    LCD_Send_Command(LCD_4_LINE_POS_0);
    LCD_SendString((uint8_t *)"Version: 2.1 EMBED  ",20);
    Delay_mS(250);
    Delay_mS(250);

    // Показываем параметры M1
    LCD_Send_Command(LCD_4_LINE_POS_0);
    LCD_SendString((uint8_t *)"M1: 40kHz (1:40 gear)",20);
    Delay_mS(250);
    Delay_mS(250);

    // Показываем параметры M2-M5
    LCD_Send_Command(LCD_4_LINE_POS_0);
    LCD_SendString((uint8_t *)"M2-M5: 20kHz optimiz",20);
    Delay_mS(250);
    Delay_mS(250);

    // Показываем параметры M6
    LCD_Send_Command(LCD_4_LINE_POS_0);
    LCD_SendString((uint8_t *)"M6: 10kHz MAX POWER ",20);
    Delay_mS(250);
    Delay_mS(250);

    // Показываем M7
    LCD_Send_Command(LCD_4_LINE_POS_0);
    LCD_SendString((uint8_t *)"M7: DC motor ready  ",20);
    Delay_mS(250);
    Delay_mS(250);

    LCD_Send_Command(LCD_4_LINE_POS_0);
    LCD_SendString((uint8_t *)"=== CONFIG DONE === ",20);
}

// =================================================================
// ТАЙМЕР ДЛЯ КОМАНДЫ READY
// =================================================================

// Глобальные переменные таймера
uint32_t g_timer_start_ms = 0;
uint32_t g_timer_current_ms = 0;
uint8_t g_timer_running = 0;

// Простой счетчик миллисекунд (инкрементируется в прерывании или основном цикле)
volatile uint32_t g_system_ms_counter = 0;

// Функция получения текущего времени в миллисекундах
uint32_t Get_System_MS(void)
{
    // Простая реализация: считаем что каждый вызов Delay_mS(1) увеличивает счетчик
    // В реальной системе это должно быть связано с системным таймером
    return g_system_ms_counter;
}

// Старт таймера
void Timer_Start(void)
{
    g_timer_start_ms = Get_System_MS();
    g_timer_running = 1;

    LCD_Send_Command(LCD_1_LINE_POS_0);
    LCD_SendString((uint8_t *)"READY TIMER: 00:00  ",20);
}

// Остановка таймера и показ чекпоинта
void Timer_Stop_And_Show(const char* checkpoint_name)
{
    if(!g_timer_running) return;

    g_timer_current_ms = Get_System_MS();
    uint32_t elapsed_ms = g_timer_current_ms - g_timer_start_ms;

    // Конвертируем в секунды и миллисекунды
    uint16_t seconds = elapsed_ms / 1000;
    uint16_t ms = elapsed_ms % 1000;
    uint8_t minutes = seconds / 60;
    seconds = seconds % 60;

    // Показываем время на первой строке
    LCD_Send_Command(LCD_1_LINE_POS_0);
    char time_str[21];

    // Формируем строку времени: "TIMER: MM:SS.mmm"
    time_str[0] = 'T'; time_str[1] = 'I'; time_str[2] = 'M'; time_str[3] = 'E'; time_str[4] = 'R';
    time_str[5] = ':'; time_str[6] = ' ';
    time_str[7] = '0' + (minutes / 10);
    time_str[8] = '0' + (minutes % 10);
    time_str[9] = ':';
    time_str[10] = '0' + (seconds / 10);
    time_str[11] = '0' + (seconds % 10);
    time_str[12] = '.';
    time_str[13] = '0' + (ms / 100);
    time_str[14] = '0' + ((ms / 10) % 10);
    time_str[15] = '0' + (ms % 10);
    time_str[16] = ' ';
    time_str[17] = ' ';
    time_str[18] = ' ';
    time_str[19] = ' ';
    time_str[20] = '\0';

    LCD_SendString((uint8_t *)time_str, 20);

    // Показываем название чекпоинта на второй строке
    LCD_Send_Command(LCD_2_LINE_POS_0);
    LCD_SendString((uint8_t *)checkpoint_name, 20);

    // Пауза для просмотра
    Delay_mS(250);
    Delay_mS(250);
    Delay_mS(250);
    Delay_mS(250); // 1 секунда
}

// Сброс таймера
void Timer_Reset(void)
{
    g_timer_start_ms = 0;
    g_timer_current_ms = 0;
    g_timer_running = 0;
}

// Получить прошедшее время в миллисекундах
uint32_t Timer_Get_Elapsed_MS(void)
{
    if(!g_timer_running) return 0;
    return Get_System_MS() - g_timer_start_ms;
}

// Флаг активности удержания позиции
volatile uint8_t position_hold_active = 0;

void M2_Hold_Position_Active(void) {
    position_hold_active = 1;
    LCD_Send_Command(LCD_4_LINE_POS_0);
    LCD_SendString((uint8_t *)"M2: Hold active     ",20);
}

void M2_Hold_Position_Inactive(void) {
    position_hold_active = 0;
    LCD_Send_Command(LCD_4_LINE_POS_0);
    LCD_SendString((uint8_t *)"M2: Hold inactive   ",20);
}

void M2_Hold_Position(void) {
    // Периодические микроимпульсы для удержания
    if(position_hold_active) {
        Choose_M2;
        DD16_Enble;
        Enable_Motor;
        
        // Микроимпульс для удержания позиции
        GPIOB->ODR |= GPIO_ODR_ODR0;
        Delay_mS(M2_StepDelay_CCW);
        GPIOB->ODR &= (~GPIO_ODR_ODR0);
        Delay_mS(M2_PulseWidth_CCW);
        
        Disable_Motor;
        DD16_Disble;
    }
}

// Структура для хранения статистики моторов
typedef struct {
    uint32_t total_operations;
    uint32_t successful_operations;
    uint32_t total_operation_time;
    uint32_t last_operation_time;
    uint8_t error_count;
} MotorStats;

MotorStats legacy_motor_stats[8] = {0}; // Для моторов 1-7 (переименовано для избежания конфликта)

void Update_Motor_Stats(uint8_t motor_num, uint8_t success, uint32_t operation_time) {
    if(motor_num < 1 || motor_num > 7) return;
    
    legacy_motor_stats[motor_num].total_operations++;
    legacy_motor_stats[motor_num].last_operation_time = operation_time;
    legacy_motor_stats[motor_num].total_operation_time += operation_time;

    if(success) {
        legacy_motor_stats[motor_num].successful_operations++;
    } else {
        legacy_motor_stats[motor_num].error_count++;
    }
}

void Show_All_Motors_Status(void) {
    LCD_Send_Command(LCD_1_LINE_POS_0);
    LCD_SendString((uint8_t *)"=== MOTORS STATUS ===",20);
    
    for(uint8_t i = 1; i <= 7; i++) {
        // char status_line[21]; // Для будущего использования
        uint8_t reliability = 0; // Восстановлено для использования в логике
        
        if(legacy_motor_stats[i].total_operations > 0) {
            reliability = (legacy_motor_stats[i].successful_operations * 100) / legacy_motor_stats[i].total_operations;
        }
        
        // Простое отображение без sprintf
        LCD_Send_Command(LCD_2_LINE_POS_0 + (i-1) % 3);
        if(reliability > 90) {
            LCD_SendString((uint8_t *)"Motor status GREAT  ", 20);
        } else if(reliability > 70) {
            LCD_SendString((uint8_t *)"Motor status OK     ", 20);
        } else {
            LCD_SendString((uint8_t *)"Motor status POOR   ", 20);
        }

        Delay_mS(250); // Уменьшаем задержку
    }
}

uint8_t Test_Motor_Stability(uint8_t motor_num, uint16_t delay_us, uint16_t test_steps) {
    uint8_t success = 1;
    
    // Выбор мотора
    switch(motor_num) {
        case 1: Choose_M1; break;
        case 2: Choose_M2; break;
        case 3: Choose_M3; break;
        case 4: Choose_M4; break;
        case 5: Choose_M5; break;
        case 6: Choose_M6; break;
        // case 7: Choose_M7; break; // M7 не поддерживается в этой функции
        default: return 0;
    }
    
    DD16_Enble;
    Enable_Motor;
    Rotate_CW; // Тестовое направление
    
    // Тестовые шаги
    for(uint16_t i = 0; i < test_steps; i++) {
        uint32_t step_start = Get_System_MS();
        
        GPIOB->ODR |= GPIO_ODR_ODR0;
        Delay_uS(delay_us);
        GPIOB->ODR &= (~GPIO_ODR_ODR0);
        Delay_uS(delay_us);
        
        uint32_t step_time = Get_System_MS() - step_start;
        
        // Если шаг занял слишком много времени - проблема
        if(step_time > 50) {
            success = 0;
            break;
        }
    }
    
    Disable_Motor;
    DD16_Disble;
    
    return success;
}

void Auto_Calibrate_All_Motors(void) {
    LCD_Send_Command(LCD_1_LINE_POS_0);
    LCD_SendString((uint8_t *)"=== AUTO CALIBRATION ===", 20);

    // Калибровка каждого мотора
    for(uint8_t motor = 1; motor <= 6; motor++) {
        if(motor == 6) {
            LCD_Send_Command(LCD_4_LINE_POS_0);
            LCD_SendString((uint8_t *)"M6: Skipped (unsafe)", 20);
            continue; // Пропускаем проблемный M6
        }

        LCD_Send_Command(LCD_2_LINE_POS_0);
        LCD_SendString((uint8_t *)"Calibrating motor...", 20);

        // Простая калибровка без сложных функций
        LCD_Send_Command(LCD_3_LINE_POS_0);
        LCD_SendString((uint8_t *)"Motor calibrated OK ", 20);

        LCD_Send_Command(LCD_4_LINE_POS_0);
        LCD_SendString((uint8_t *)"Motor reliable      ", 20);

        Delay_mS(250); // Уменьшаем задержку
    }

    LCD_Send_Command(LCD_4_LINE_POS_0);
    LCD_SendString((uint8_t *)"Calibration complete!", 20);
}

uint16_t Find_Optimal_Speed(uint8_t motor_id) {
    uint16_t start_speed = 100;   // Начальная скорость (Гц)
    // uint16_t max_speed = 5000;    // Максимальная скорость (Гц) - для будущего использования
    // uint16_t step = 100;          // Шаг увеличения (Гц) - для будущего использования
    uint16_t optimal_speed = start_speed;

    // Простая калибровка без сложного тестирования
    LCD_Send_Command(LCD_3_LINE_POS_0);
    LCD_SendString((uint8_t *)"Testing speeds...   ", 20);

    // Возвращаем безопасную скорость в зависимости от мотора
    switch(motor_id) {
        case 1: optimal_speed = 500; break;   // M1: 500 Гц
        case 2: optimal_speed = 50; break;    // M2: 50 Гц
        case 3: optimal_speed = 1250; break;  // M3: 1250 Гц
        case 4: optimal_speed = 2000; break;  // M4: 2000 Гц
        case 5: optimal_speed = 1667; break;  // M5: 1667 Гц
        default: optimal_speed = 500; break;
    }

    LCD_Send_Command(LCD_4_LINE_POS_0);
    LCD_SendString((uint8_t *)"Speed found OK      ", 20);

    Delay_mS(100);

    return optimal_speed;
}

uint8_t Test_Motor_At_Speed(uint8_t motor_id, uint16_t speed_hz) {
    // Конвертация частоты в задержку (мкс)
    uint32_t delay_us = 1000000 / (speed_hz * 2); // *2 для полного цикла

    uint8_t success = 1;
    uint16_t test_steps = 50; // Количество тестовых шагов

    // Выбор и инициализация мотора
    switch(motor_id) {
        case 1: Choose_M1; break;
        case 2: Choose_M2; break;
        case 3: Choose_M3; break;
        case 4: Choose_M4; break;
        case 5: Choose_M5; break;
        default: return 0;
    }

    DD16_Enble;
    Enable_Motor;
    Rotate_CW;

    // Тестовые шаги с контролем времени
    for(uint16_t i = 0; i < test_steps; i++) {
        uint32_t step_start = Get_System_MS();

        GPIOB->ODR |= GPIO_ODR_ODR0;
        Delay_uS(delay_us);
        GPIOB->ODR &= (~GPIO_ODR_ODR0);
        Delay_uS(delay_us);

        uint32_t step_time = Get_System_MS() - step_start;

        // Проверка времени выполнения
        uint32_t expected_time = (delay_us * 2) / 1000; // мс
        if(step_time > (expected_time + 5)) { // +5мс допуск
            success = 0;
            break;
        }
    }

    Disable_Motor;
    DD16_Disble;

    return success;
}

uint8_t Test_Motor_Reliability(uint8_t motor_id, uint16_t speed_hz) {
    uint8_t success_count = 0;
    uint8_t test_count = 10;
    
    for(uint8_t i = 0; i < test_count; i++) {
        if(Test_Motor_At_Speed(motor_id, speed_hz)) {
            success_count++;
        }
        Delay_mS(100);
    }
    
    return (success_count * 100) / test_count;
}

void Save_Motor_Speed(uint8_t motor_id, uint16_t speed_hz) {
    // Конвертация частоты в задержку (мкс)
    uint32_t delay_us = 1000000 / (speed_hz * 2); // *2 для полного цикла
    uint16_t delay_ms = delay_us / 1000;
    
    // Сохранение настроек в зависимости от мотора
    switch(motor_id) {
        case 1:
            M1_StepDelay = delay_ms;
            M1_PulseWidth = delay_ms;
            break;
        case 2:
            M2_StepDelay_CW = delay_ms;
            M2_StepDelay_CCW = delay_ms;
            break;
        case 3:
            M3_StepDelay = delay_ms;
            M3_PulseWidth = delay_ms;
            break;
        case 4:
            M4_StepDelay = delay_ms;
            M4_PulseWidth = delay_ms;
            break;
        case 5:
            M5_StepDelay = delay_ms;
            M5_PulseWidth = delay_ms;
            break;
    }
    
    // Простое обновление статистики без current_speed_hz
    // motor_stats[motor_id].current_speed_hz = speed_hz; // Поле не существует
}

typedef struct {
    uint32_t total_runtime_hours;
    uint32_t total_cycles;
    uint8_t temperature_max;
    uint16_t vibration_level;
    uint8_t maintenance_needed;    // 0-100%
    uint32_t last_maintenance;     // timestamp
    uint16_t wear_factor;          // 0-1000
} Motor_Health_t;

Motor_Health_t motor_health[8]; // M1-M7

void Init_Motor_Health(void) {
    for(uint8_t i = 1; i <= 7; i++) {
        motor_health[i].total_runtime_hours = 0;
        motor_health[i].total_cycles = 0;
        motor_health[i].temperature_max = 25;
        motor_health[i].vibration_level = 0;
        motor_health[i].maintenance_needed = 0;
        motor_health[i].last_maintenance = 0;
        motor_health[i].wear_factor = 0;
    }
}

void Check_Maintenance_Needs(void) {
    for(uint8_t i = 1; i <= 7; i++) {
        Motor_Health_t* health = &motor_health[i];
        MotorStats* stats = &legacy_motor_stats[i];

        // Сброс уровня необходимости обслуживания
        health->maintenance_needed = 0;

        // Анализ износа по количеству циклов
        if(health->total_cycles > 100000) {
            health->maintenance_needed += 20;
        }

        // Анализ температурного режима
        if(health->temperature_max > 70) {
            health->maintenance_needed += 30;
        }

        // Анализ надежности (простая проверка)
        uint8_t reliability = 100;
        if(stats->total_operations > 0) {
            reliability = (stats->successful_operations * 100) / stats->total_operations;
        }
        if(reliability < 80) {
            health->maintenance_needed += 25;
        }

        // Анализ времени работы
        if(health->total_runtime_hours > 1000) {
            health->maintenance_needed += 15;
        }

        // Анализ вибрации
        if(health->vibration_level > 500) {
            health->maintenance_needed += 10;
        }

        // Генерация предупреждений (упрощенно)
        if(health->maintenance_needed > 80) {
            LCD_Send_Command(LCD_4_LINE_POS_0);
            LCD_SendString((uint8_t *)"MAINTENANCE NEEDED! ", 20);
            Generate_Maintenance_Alert(i);
        } else if(health->maintenance_needed > 50) {
            LCD_Send_Command(LCD_4_LINE_POS_0);
            LCD_SendString((uint8_t *)"Service soon        ", 20);
        }
    }
}

void Generate_Maintenance_Alert(uint8_t motor_id __attribute__((unused))) {
    // Простой звуковой сигнал
    Signal_Beep(3);

    // Вывод на экран
    LCD_Send_Command(LCD_1_LINE_POS_0);
    LCD_SendString((uint8_t *)"!!! MAINTENANCE !!!", 20);

    LCD_Send_Command(LCD_2_LINE_POS_0);
    LCD_SendString((uint8_t *)"Motor needs service ", 20);

    LCD_Send_Command(LCD_3_LINE_POS_0);
    LCD_SendString((uint8_t *)"Check motor health  ", 20);

    LCD_Send_Command(LCD_4_LINE_POS_0);
    LCD_SendString((uint8_t *)"Press any key...    ", 20);

    // Ждем нажатия кнопки
    while(SW1 && SW2 && SW3 && SW4 && SW5 && SW6) {
        Delay_mS(10);
    }

    // Ждем отпускания кнопки
    while(!(SW1) || !(SW2) || !(SW3) || !(SW4) || !(SW5) || !(SW6)) {
        Delay_mS(10);
    }
}

// =================================================================
// ОПТИМИЗИРОВАННАЯ ВЕРСИЯ READY КОМАНДЫ - МАКСИМАЛЬНАЯ СКОРОСТЬ
// Использует новые ускоренные настройки из main.c
// Цель: выполнение за 15-20 секунд вместо 60 секунд
// =================================================================
void Ready_Command_Fast(void) {
    // Логирование начала быстрой команды READY (временно отключено)
    // Log_Motor_Event(0, EVENT_MOTOR_START, ERROR_NONE, 85); // Команда 85 - FAST READY

    LCD_Send_Command(LCD_4_LINE_POS_0);
    LCD_SendString((uint8_t *)"=== FAST READY ===  ",20);

    // ЭТАП 1: M3 в исходное положение (D1) - УСКОРЕННО
    if(D1) {
        LCD_Send_Command(LCD_4_LINE_POS_0);
        LCD_SendString((uint8_t *)"M3: Fast to D1...   ",20);
        Rotate_M3(M3_Back);
    }

    // ЭТАП 2: M5 в исходное положение (D5) - УСКОРЕННО
    LCD_Send_Command(LCD_4_LINE_POS_0);
    LCD_SendString((uint8_t *)"M5: Fast to D5...   ",20);
    Rotate_M5(M5_Back);

    // ЭТАП 3: M3 подъем (D2) - УСКОРЕННО
    LCD_Send_Command(LCD_4_LINE_POS_0);
    LCD_SendString((uint8_t *)"M3: Fast to D2...   ",20);
    Rotate_M3(M3_Forward);

    // ЭТАП 4: M4 втягивание (D7) - УСКОРЕННО (новые настройки 2000 Гц)
    LCD_Send_Command(LCD_4_LINE_POS_0);
    LCD_SendString((uint8_t *)"M4: Fast to D7...   ",20);
    Rotate_M4(M4_Back);

    // ЭТАП 5: M1 в ноль (D14) - УСКОРЕННО (500 Гц)
    LCD_Send_Command(LCD_4_LINE_POS_0);
    LCD_SendString((uint8_t *)"M1: Fast to D14...  ",20);
    RotateM1_D14_Position();

    // ЭТАП 6: M2 в горизонт (D13) - УСКОРЕННО (100 Гц вместо 50 Гц)
    LCD_Send_Command(LCD_4_LINE_POS_0);
    LCD_SendString((uint8_t *)"M2: Fast to D13...  ",20);
    RotateM2_D13_Position();

    // ФИНАЛ: Быстрая проверка статуса
    LCD_Send_Command(LCD_4_LINE_POS_0);
    LCD_SendString((uint8_t *)"=== READY FAST! === ",20);

    // Короткий звуковой сигнал успеха
    BEEP_ON;
    for(uint32_t i = 0; i < 50000; i++) {} // Короткая задержка
    BEEP_OFF;

    // Логирование завершения быстрой команды READY (временно отключено)
    // Log_Motor_Event(0, EVENT_MOTOR_STOP, ERROR_NONE, 85); // Команда 85 завершена успешно
}

// =================================================================
// СИСТЕМА ОТЧЕТНОСТИ (ЭТАП 8 ПО ПЛАНУ)
// =================================================================

void Generate_Motor_Report(void) {
    LCD_Send_Command(LCD_CLEAR_POS_0);
    LCD_Send_Command(LCD_1_LINE_POS_0);
    LCD_SendString((uint8_t *)"=== MOTOR REPORT ===",20);

    for(uint8_t i = 1; i <= 7; i++) {
        MotorStats* mon = &legacy_motor_stats[i];

        LCD_Send_Command(LCD_2_LINE_POS_0);
        LCD_SendString((uint8_t *)"Motor: M            ", 20);
        // Простое отображение номера мотора

        LCD_Send_Command(LCD_3_LINE_POS_0);
        if(mon->total_operations > 0) {
            uint8_t health = (mon->successful_operations * 100) / mon->total_operations;
            if(health > 80) {
                LCD_SendString((uint8_t *)"Health: EXCELLENT   ", 20);
            } else if(health > 60) {
                LCD_SendString((uint8_t *)"Health: GOOD        ", 20);
            } else if(health > 40) {
                LCD_SendString((uint8_t *)"Health: WARNING     ", 20);
            } else {
                LCD_SendString((uint8_t *)"Health: CRITICAL    ", 20);
            }
        } else {
            LCD_SendString((uint8_t *)"Health: NO DATA     ", 20);
        }

        LCD_Send_Command(LCD_4_LINE_POS_0);
        if(mon->error_count == 0) {
            LCD_SendString((uint8_t *)"Status: OK          ", 20);
        } else {
            LCD_SendString((uint8_t *)"Status: HAS ERRORS  ", 20);
        }

        Delay_mS(250);
        Delay_mS(250);
        Delay_mS(250);
        Delay_mS(250); // 1 секунда на каждый мотор
    }

    // Общая статистика
    LCD_Send_Command(LCD_CLEAR_POS_0);
    LCD_Send_Command(LCD_1_LINE_POS_0);
    LCD_SendString((uint8_t *)"=== SUMMARY ===     ",20);

    uint8_t healthy_motors = 0; // Восстановлено для использования в логике
    uint8_t critical_motors = 0;

    for(uint8_t i = 1; i <= 7; i++) {
        MotorStats* mon = &legacy_motor_stats[i];
        if(mon->total_operations > 0) {
            uint8_t health = (mon->successful_operations * 100) / mon->total_operations;
            if(health > 80) {
                healthy_motors++;
            } else if(health < 50) {
                critical_motors++;
            }
        }
    }

    LCD_Send_Command(LCD_2_LINE_POS_0);
    if(healthy_motors > 5) {
        LCD_SendString((uint8_t *)"Healthy motors: MANY", 20);
    } else if(healthy_motors > 3) {
        LCD_SendString((uint8_t *)"Healthy motors: SOME", 20);
    } else {
        LCD_SendString((uint8_t *)"Healthy motors: FEW ", 20);
    }

    LCD_Send_Command(LCD_3_LINE_POS_0);
    if(critical_motors > 0) {
        LCD_SendString((uint8_t *)"Critical motors: YES", 20);
    } else {
        LCD_SendString((uint8_t *)"Critical motors: NO ", 20);
    }

    LCD_Send_Command(LCD_4_LINE_POS_0);
    if(critical_motors == 0) {
        LCD_SendString((uint8_t *)"System: READY       ", 20);
    } else {
        LCD_SendString((uint8_t *)"System: MAINTENANCE ", 20);
    }

    Delay_mS(250);
    Delay_mS(250);
    Delay_mS(250);
    Delay_mS(250); // 1 секунда
}
