#ifndef MAIN_H
#define MAIN_H

#endif

#include "stm32f10x.h"
#include "main.h"
#include "I2C.h"
#include "lcd.h"

uint8_t LCD_Current[7] = {0};//To store simbols of current value
uint8_t LCD_Voltage[7] = {0};//To store simbols of current value

uint8_t LCD_Char0[] = {0x00,0x00,0x00,0x10,0x10,0x10,0x00,0x00};

uint8_t LCD_Char1[] = {0x00,0x00,0x00,0x18,0x18,0x18,0x00,0x00};

uint8_t LCD_Char2[] = {0x00,0x00,0x00,0x1C,0x1C,0x1C,0x00,0x00};

uint8_t LCD_Char3[] = {0x00,0x00,0x00,0x1E,0x1E,0x1E,0x00,0x00};

uint8_t LCD_Char4[] = {0x00,0x00,0x00,0x1F,0x1F,0x1F,0x00,0x00};

uint8_t Chars0[] = {0x00,' ',' ',' ',' '};

uint8_t Chars1[] = {0x01,' ',' ',' ',' '};

uint8_t Chars2[] = {0x02,' ',' ',' ',' '};

uint8_t Chars3[] = {0x03,' ',' ',' ',' '};

uint8_t Chars4[] = {0x04,' ',' ',' ',' '};

//Sends 8 bit command:
void LCD_Send_Command(uint8_t data) 
 {
	 uint8_t addr = 0x27; //PCF8574 address
	 
	 uint8_t command = 0x0C;//1100 - BackLight ON, E == 1, RS == 0, Write == 0, 
 
	 uint8_t LSB_Data = 0;
	 uint8_t MSB_Data = 0;
	 
	 MSB_Data = data & 0xF0;
	 LSB_Data = (uint8_t)((data << 4) & 0xF0);
	 
	 command |= MSB_Data;//4 MSB
		 
	 addr = (uint8_t)(addr << 1);
		 
     //I2C1_Start:
/****************************************/
	 I2C1 -> CR1 |=  I2C_CR1_START;	
   
		//Waite for EV5 (Set up bit SB):
   while(!(I2C1 -> SR1 & I2C_SR1_SB))
	  {
			
	  }	 

	 (void) I2C1 -> SR1; //to clear bit SB

	 I2C1 -> DR = addr;
		
	 //Wait for End of EV6 transmit:
	 while(!(I2C1 -> SR1 & I2C_SR1_ADDR))
	  {
	  }
		//Clear bit ADDR:
	 (void) I2C1 -> SR1;
	 (void) I2C1 -> SR2;		
/****************************************/		
		
			//Write 4 MSB bits
     I2C1->DR = command; //
     while (!(I2C1->SR1 & I2C_SR1_TXE))
      {
      }	

     Delay_mS(1);		
/*******************************************/
		 command &= 0xFB;//Clear E bit

     I2C1->DR = command; //Send second time with E = 0
     while (!(I2C1->SR1 & I2C_SR1_TXE))
      {
      }	

     Delay_mS(1);		
/*******************************************/			
		 
		//Write 4 LSB bits
 
		command = 0x0C;
		 
		command |= LSB_Data;//4 LSB
    			
		 I2C1->DR = command; //Send LSB first time:
     while (!(I2C1->SR1 & I2C_SR1_TXE))
      {
      }	

     Delay_mS(1);		

		 command &= 0xFB;//Clear E bit

     I2C1->DR = command; //Send LSB second time:
     while (!(I2C1->SR1 & I2C_SR1_TXE))
      {
      }	

     Delay_mS(1);			

     I2C1_STOP;
     //Delay_mS(5);			
 } //End of LCD_Send_Command()
 
 
 //Sends 4 bit command:
void LCD_Send_4BitCmd(uint8_t cmd)
{
	uint8_t addr = 0x27;
	uint8_t command = 0x0C;//BL, E, R/W, RS,  - 1100

	command = (command | cmd);// <cmd><0x0C>
	addr = (uint8_t)(addr << 1);
	
     //I2C1_Start:
/****************************************/
	 I2C1 -> CR1 |=  I2C_CR1_START;	
   
		//Waite for EV5 (Set up bit SB):
   while(!(I2C1 -> SR1 & I2C_SR1_SB))
	  {
			
	  }	 

	 (void) I2C1 -> SR1; //to clear bit SB

	 I2C1 -> DR = addr;
		
	 //Wait for End of EV6 transmit:
	 while(!(I2C1 -> SR1 & I2C_SR1_ADDR))
	  {
	  }
		//Clear bit ADDR:
	 (void) I2C1 -> SR1;
	 (void) I2C1 -> SR2;	
	 
/****************************************/	

		//Write 4 bits command 1-st time, E == 1:
     I2C1->DR = command; //
     while (!(I2C1->SR1 & I2C_SR1_TXE))
      {
      }	

     Delay_mS(1);	

		 command &= 0xFB;// E == 0

		//Write 4 bits command 2-nd time, E == 0:
     I2C1->DR = command; //
     while (!(I2C1->SR1 & I2C_SR1_TXE))
      {
      }	
     Delay_mS(1);			
     I2C1_STOP;		 	
}	
 

void LCD_Send_Data(uint8_t data) 
 {
	 
	 uint8_t addr = 0x27;
	 
	 uint8_t command = 0x0D;//BL, E, R/W, RS,  - 1101
	 
	 uint8_t LSB_Data = 0;
	 uint8_t MSB_Data = 0;
	 
	 
	 MSB_Data = data & 0xF0;
	 LSB_Data = (uint8_t)((data << 4) & 0xF0);
	 
	 command |= MSB_Data;//4 MSB
		 
	 addr = (uint8_t)(addr << 1);
		 
     //I2C1_Start:
/****************************************/
	 I2C1 -> CR1 |=  I2C_CR1_START;	
   
		//Waite for EV5 (Set up bit SB):
   while(!(I2C1 -> SR1 & I2C_SR1_SB))
	  {
			
	  }	 

	 (void) I2C1 -> SR1; //to clear bit SB

	 I2C1 -> DR = addr;
		
	 //Wait for End of EV6 transmit:
	 while(!(I2C1 -> SR1 & I2C_SR1_ADDR))
	  {
	  }
		//Clear bit ADDR:
	 (void) I2C1 -> SR1;
	 (void) I2C1 -> SR2;
		
	  Delay_mS(1);
/****************************************/		
		
			//Write 4 MSB bits
     I2C1->DR = command; //
     while (!(I2C1->SR1 & I2C_SR1_TXE))
      {
      }	

     Delay_mS(1);		
/*******************************************/
		 command &= 0xFB;//Clear E bit - 1011

     I2C1->DR = command; //Send second time with E == 0
     while (!(I2C1->SR1 & I2C_SR1_TXE))
      {
      }	

     Delay_mS(1);		
/*******************************************/			
		 
		//Write 4 LSB bits
	  command = 0x0D;
		 
		command |= LSB_Data;//4 LSB
    			
		 I2C1->DR = command; //Send LSB first time:
     while (!(I2C1->SR1 & I2C_SR1_TXE))
      {
      }	

     Delay_mS(1);		

		 command &= 0xFB;//Clear E bit 1011

     I2C1->DR = command; //Send LSB second time:
     while (!(I2C1->SR1 & I2C_SR1_TXE))
      {
      }	

     I2C1_STOP;
		 //Delay_mS(1);
		
 } //End of LCD_Send_Data() 
 
void LCD_Setup(void)
{
	
	 /*------ LCD Ini--------*/
/**********************************************************************/
	LCD_Send_4BitCmd(0x30);
	 Delay_mS(5);	
	LCD_Send_4BitCmd(0x30);
	 Delay_mS(2);	
	LCD_Send_4BitCmd(0x30);
	 Delay_mS(2);	
	LCD_Send_4BitCmd(0x20);
	 Delay_mS(2);	
	
	
/**********************************************************************/	

   LCD_Send_Command(0x28);//4 bits comunication	
	 Delay_mS(2);		
		
   LCD_Send_Command(0x08);		
	 Delay_mS(2);
		
	 LCD_Send_Command(0x01);
	 Delay_mS(2);
		
	 LCD_Send_Command(0x06);
	 Delay_mS(2);

	 LCD_Send_Command(0x0C); //LCD_ON, Cursor and blinking OFF
	 Delay_mS(2);
		
	 //LCD_Send_Command(0x0F);//LCD_ON, Cursor_ON
	 //LCD_Send_Command(0x0C);//LCD_ON, Cursor_OFF, Blink - OFF
	 Delay_mS(2);
 }

void Test_1(void)
{
	

  //LCD_SendString((uint8_t *)"Test_1");
	

	 
}

void LCD_CursorGoToLeft(uint8_t position)
{
		for(uint8_t i = position; i>0; i--)
	    {
			  LCD_Send_Command(0x10);
	      Delay_mS(1);
		  }
}

void LCD_CursorGoToRight(uint8_t position)
{
		for(uint8_t i = 0; i<position; i++)
	    {
			  LCD_Send_Command(0x14);
	      Delay_mS(1);
		  }
}

void LCD_SendString(uint8_t *string, uint8_t number)
{
	for(uint8_t i = 0; i<number; i++)
	{
	  LCD_Send_Data(string[i]);	
	}
}

void LCD_Set_CGRAM(uint8_t data) 
 {
	 uint8_t addr = 0x27; //PCF8574 address
	 
	 uint8_t command = 0x0C;//1100 - BackLight ON, E == 1, Write == 0, RS == 0  
 
	 uint8_t LSB_Data = 0;
	 uint8_t MSB_Data = 0;
	 
	 data &= 0x7F;//real addr: A5 - A0; A7,A6 always: [0:1]
	 data |= 0x40;
	 
	 MSB_Data = data & 0xF0;
	 LSB_Data = (uint8_t)((data << 4) & 0xF0);
	 
	 command |= MSB_Data;//4 MSB
		 
	 addr = (uint8_t)(addr << 1);
		 
     //I2C1_Start:
/****************************************/
	 I2C1 -> CR1 |=  I2C_CR1_START;	
   
		//Waite for EV5 (Set up bit SB):
   while(!(I2C1 -> SR1 & I2C_SR1_SB))
	  {
			
	  }	 

	 (void) I2C1 -> SR1; //to clear bit SB

	 I2C1 -> DR = addr;
		
	 //Wait for End of EV6 transmit:
	 while(!(I2C1 -> SR1 & I2C_SR1_ADDR))
	  {
	  }
		//Clear bit ADDR:
	 (void) I2C1 -> SR1;
	 (void) I2C1 -> SR2;		
/****************************************/		
		
			//Write 4 MSB bits
     I2C1->DR = command; //
     while (!(I2C1->SR1 & I2C_SR1_TXE))
      {
      }	

     Delay_mS(1);		
/*******************************************/
		 command &= 0xFB;//Clear E bit

     I2C1->DR = command; //Send second time with E = 0
     while (!(I2C1->SR1 & I2C_SR1_TXE))
      {
      }	

     Delay_mS(1);		
/*******************************************/			
		 
		//Write 4 LSB bits
 
		command = 0x0C;
		 
		command |= LSB_Data;//4 LSB
    			
		 I2C1->DR = command; //Send LSB first time:
     while (!(I2C1->SR1 & I2C_SR1_TXE))
      {
      }	

     Delay_mS(1);		

		 command &= 0xFB;//Clear E bit

     I2C1->DR = command; //Send LSB second time:
     while (!(I2C1->SR1 & I2C_SR1_TXE))
      {
      }	

     Delay_mS(1);			

     I2C1_STOP;
     //Delay_mS(5);			
 } //End of void LCD_Set_CGRAM
 
void LCD_Write_CGRAM(uint8_t *data) 
 {
	 uint8_t addr = 0x27; //PCF8574 address
	 
	 uint8_t command = 0x0D;//1101 - BackLight ON , E == 1, R/W == 0,RS == 1
 
	 uint8_t LSB_Data = 0;
	 uint8_t MSB_Data = 0;
	 
	 addr = (uint8_t)(addr << 1);
	 
	 for(uint8_t i=0; i<=7; i++)
	  {
	    LSB_Data = 0;
	    MSB_Data = 0;
			command = 0x0D;

	    MSB_Data = *(data+i) & 0xF0;
	    LSB_Data = (uint8_t)((*(data+i) << 4) & 0xF0);
	 
	    command |= MSB_Data;//4 MSB
		 
	    		 
          //I2C1_Start:
/****************************************/
	    I2C1 -> CR1 |=  I2C_CR1_START;	
   
		    //Waite for EV5 (Set up bit SB):
      while(!(I2C1 -> SR1 & I2C_SR1_SB))
	     {
			
	     }	 

	    (void) I2C1 -> SR1; //to clear bit SB

	    I2C1 -> DR = addr;
		
	     //Wait for End of EV6 transmit:
	    while(!(I2C1 -> SR1 & I2C_SR1_ADDR))
	     {
	     }
		      //Clear bit ADDR:
	    (void) I2C1 -> SR1;
	    (void) I2C1 -> SR2;		
/****************************************/		
		
			   //Write 4 MSB bits
      I2C1->DR = command; //
      while (!(I2C1->SR1 & I2C_SR1_TXE))
      {
      }	

      Delay_mS(1);		
/*******************************************/
		 command = 0x09;//Clear E bit

     I2C1->DR = command; //Send second time with E = 0
     while (!(I2C1->SR1 & I2C_SR1_TXE))
      {
      }	

     Delay_mS(1);		
/*******************************************/			
		 
		//Write 4 LSB bits
 
		command = 0x0D;
		 
		command |= LSB_Data;//4 LSB
    			
		 I2C1->DR = command; //Send LSB first time:
     while (!(I2C1->SR1 & I2C_SR1_TXE))
      {
      }	

     Delay_mS(1);		

		 command = 0x09;//Clear E bit

     I2C1->DR = command; //Send LSB second time:
     while (!(I2C1->SR1 & I2C_SR1_TXE))
      {
      }	

     //I2C1_STOP;
		 Delay_mS(1);
		}//END For()	
   I2C1_STOP;		
     //Delay_mS(5);			
 } //End of LCD_Write_CGRAM(uint8_t *data)

void Current_To_LCD(uint8_t *buf, uint8_t *lcd_buf)
{
	if(buf[0] > 0x30)
	  {
	   lcd_buf[0] = buf[0]; //Integer part
	  }                      //of Current value
	else
    lcd_buf[0] = ' ';	
	
	if(buf[1] > 0x30)
	  {	
	     lcd_buf[1] = buf[1]; 
		}
	else
    lcd_buf[1] = 0x30;
	
  lcd_buf[2] = '.';

	if(buf[2] > 0x30)
	  {		
	    lcd_buf[3] = buf[2]; //Fraction part
		}
	else
    lcd_buf[3] = 0x30;
	
	if(buf[3] > 0x30)
	  {		
	    lcd_buf[4] = buf[3]; //of Current value
		}
	else
    lcd_buf[4] = 0x30;	
	
	lcd_buf[5] = ' ';	   //'Space'
	lcd_buf[6] = 'A';	
}//End OF
 
void Voltage_To_LCD(uint8_t *buf, uint8_t *lcd_buf)
{
	if(buf[0] > 0x30)
	  {
	   lcd_buf[0] = buf[0]; //Integer part
	  }                      //of Voltage value
	else
    lcd_buf[0] = ' ';	
	
	if(buf[1] > 0x30)
	  {	
	     lcd_buf[1] = buf[1]; 
		}
	else
    lcd_buf[1] = 0x30;
	
  lcd_buf[2] = '.';

	if(buf[2] > 0x30)
	  {		
	    lcd_buf[3] = buf[2]; //Fraction part
		}
	else
    lcd_buf[3] = 0x30;
	
	if(buf[3] > 0x30)
	  {		
	    lcd_buf[4] = buf[3]; //of Current value
		}
	else
    lcd_buf[4] = 0x30;	
	
	lcd_buf[5] = ' ';	   //'Space'
	lcd_buf[6] = 'V';	
}//End OF Voltage_To_LCD(uint8_t *buf, uint8_t *lcd_buf)

void LCD_UserChars(void)
{
}
