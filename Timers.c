#ifndef MAIN_H
#define MAIN_H

#include "main.h"
#include "IO_gpio.h"
#endif

#include "stm32f10x.h"
#include "Timers.h"

void TIM2_IRQ<PERSON>andler(void);

void TIM2_IRQ<PERSON>andler(void)
{
  TIM2->SR &= ~TIM_SR_UIF; //Clear UIF Flag (Update Interrupt flag)
	//TIM2_INT_Counter++;	
}

void SetupTimers(void)
{
//Setup TIM4 for LED blinking:
	 TIM4->PSC = 36000; // The counter clock frequency CK_CNT is equal to fCK_PSC / (PSC[15:0] + 1), 72MHz / 36000 = 2kHz
   TIM4->ARR = 1000;  // Couter counts till thise value - 1000 (1 Interrupt/0.5 sec) (1 / 2 kHz * (TIM4->ARR))
   TIM4->DIER |= TIM_DIER_UIE; // Enable TIM4 update interrupt
   TIM4->CR1 |= TIM_CR1_CEN;   // Start count  
	 
// Setup TIM2, to meassure some time intervals
// setting up  - TIM2, each counter tick is 10uS
	 TIM2->PSC = (uint16_t)840; // The counter clock frequency CK_CNT is equal to fCK_PSC / (PSC[15:0] + 1)
	 TIM2->EGR |= TIM_EGR_UG;
   TIM2->ARR = 0xFFFF;  // Couter counts till thise value, then restarts from 0
   //TIM2->DIER |= TIM_DIER_UIE; // Enable TIM2 update interrupt
   //TIM2->CR1 |= TIM_CR1_CEN;   // Start count 

// Setup TIM3, to meassure delay in uS:
// 
	 TIM3->PSC = (36 - 1); // The counter clock frequency CK_CNT is equal to fCK_PSC / (PSC[15:0] + 1)
   //TIMER_PSC(TIMER3) = (uint16_t)360; // The counter clock frequency CK_CNT is equal to fCK_PSC / (PSC[15:0] + 1)
	 TIM3->EGR |= TIM_EGR_UG;
   TIM3->ARR = 0xFFFF;  // Auto reload value, Couter counts till thise value - 719 	

}//End OF SetupTimers(void)

//Just for testing EVB LED blinking 
void TIM4_IRQHandler(void) //TIM4_IRQHandler
{
  TIM4->SR &= ~TIM_SR_UIF; //Clear UIF Flag (Update Interrupt flag)
	//GPIOB->ODR ^= GPIO_ODR_ODR6;
	GPIOC->ODR ^= GPIO_ODR_ODR13;// Invert PC13 (LED) State
//GPIOA->ODR ^= GPIO_ODR_ODR5;	
//GPIOA->ODR ^= GPIO_ODR_ODR15;
	//GPIOB->ODR ^= GPIO_ODR_ODR5;//For SUMMER Testing
	//StartTimer++;
}

