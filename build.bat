@echo off
echo Setting up build environment...

REM Set temporary directories to avoid Cyrillic path issues
set TEMP=C:\Temp
set TMP=C:\Temp

REM Create temp directory if it doesn't exist
if not exist "C:\Temp" mkdir "C:\Temp"

echo Temporary directories set to: %TEMP%

REM Try to find Keil installation
set KEIL_PATH=""
if exist "%USERPROFILE%\AppData\Local\Keil_v5\UV4\UV4.exe" set KEIL_PATH="%USERPROFILE%\AppData\Local\Keil_v5\UV4\UV4.exe"
if exist "C:\Keil_v5\UV4\UV4.exe" set KEIL_PATH="C:\Keil_v5\UV4\UV4.exe"
if exist "C:\Keil\UV4\UV4.exe" set KEIL_PATH="C:\Keil\UV4\UV4.exe"
if exist "C:\Program Files\Keil\UV4\UV4.exe" set KEIL_PATH="C:\Program Files\Keil\UV4\UV4.exe"
if exist "C:\Program Files (x86)\Keil\UV4\UV4.exe" set KEIL_PATH="C:\Program Files (x86)\Keil\UV4\UV4.exe"

if %KEIL_PATH%=="" (
    echo ERROR: Keil uVision not found!
    echo Please install Keil uVision or update the path in this script.
    pause
    exit /b 1
)

echo Found Keil at: %KEIL_PATH%

REM Clean previous build
echo Cleaning previous build...
if exist "Objects\*.o" del /q "Objects\*.o"
if exist "Objects\*.d" del /q "Objects\*.d"
if exist "Objects\*.axf" del /q "Objects\*.axf"
if exist "Objects\*.hex" del /q "Objects\*.hex"

echo Building project...
%KEIL_PATH% -b Servo.uvprojx

if %ERRORLEVEL%==0 (
    echo Build successful!
    if exist "Objects\Servo.hex" (
        echo HEX file created: Objects\Servo.hex
    )
) else (
    echo Build failed with error code: %ERRORLEVEL%
)

pause
