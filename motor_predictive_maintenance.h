#ifndef MOTOR_PREDICTIVE_MAINTENANCE_H
#define MOTOR_PREDICTIVE_MAINTENANCE_H

#include <stdint.h>
#include "motor_unified_config.h"

// =================================================================
// КОНСТАНТЫ ПРЕДИКТИВНОГО ОБСЛУЖИВАНИЯ
// =================================================================

// Пороги для прогнозирования
#define MAINTENANCE_CRITICAL_THRESHOLD      20  // Критический уровень (%)
#define MAINTENANCE_WARNING_THRESHOLD       40  // Предупреждение (%)
#define MAINTENANCE_GOOD_THRESHOLD          70  // Хорошее состояние (%)

// Интервалы анализа
#define ANALYSIS_WINDOW_OPERATIONS          100 // Окно анализа операций
#define TREND_ANALYSIS_POINTS               10  // Точки для анализа тренда
#define PREDICTION_HORIZON_HOURS            24  // Горизонт прогнозирования (часы)

// Коэффициенты весов для прогнозирования
#define WEIGHT_SUCCESS_RATE                 30  // Вес успешности операций
#define WEIGHT_RESPONSE_TIME                25  // Вес времени отклика
#define WEIGHT_TEMPERATURE                  20  // Вес температуры
#define WEIGHT_VIBRATION                    15  // Вес вибрации
#define WEIGHT_USAGE_INTENSITY              10  // Вес интенсивности использования

// Статусы обслуживания
#define MAINTENANCE_STATUS_EXCELLENT        0   // Отличное состояние
#define MAINTENANCE_STATUS_GOOD             1   // Хорошее состояние
#define MAINTENANCE_STATUS_WARNING          2   // Требует внимания
#define MAINTENANCE_STATUS_CRITICAL         3   // Критическое состояние
#define MAINTENANCE_STATUS_FAILURE          4   // Отказ

// =================================================================
// СТРУКТУРЫ ДАННЫХ
// =================================================================

// Точка данных для анализа тренда
typedef struct {
    uint32_t timestamp;                 // Временная метка
    uint8_t success_rate;               // Процент успешных операций
    uint16_t avg_response_time;         // Среднее время отклика (мс)
    uint8_t temperature;                // Температура (°C)
    uint8_t vibration_level;            // Уровень вибрации (0-100)
    uint16_t operations_count;          // Количество операций
} trend_data_point_t;

// Результат анализа тренда
typedef struct {
    int8_t success_rate_trend;          // Тренд успешности (-100 до +100)
    int8_t response_time_trend;         // Тренд времени отклика
    int8_t temperature_trend;           // Тренд температуры
    int8_t vibration_trend;             // Тренд вибрации
    uint8_t trend_confidence;           // Уверенность в тренде (0-100%)
} trend_analysis_t;

// Прогноз состояния мотора
typedef struct {
    uint8_t motor_id;                   // ID мотора
    uint8_t current_health_score;       // Текущий показатель здоровья (0-100)
    uint8_t predicted_health_score;     // Прогнозируемый показатель
    uint16_t hours_to_maintenance;      // Часов до обслуживания
    uint16_t hours_to_failure;          // Часов до возможного отказа
    uint8_t maintenance_priority;       // Приоритет обслуживания (1-5)
    uint8_t confidence_level;           // Уверенность прогноза (0-100%)
    char recommendation[32];            // Рекомендация по обслуживанию
} maintenance_prediction_t;

// Расписание обслуживания
typedef struct {
    uint8_t motor_id;                   // ID мотора
    uint8_t maintenance_type;           // Тип обслуживания
    uint32_t scheduled_time;            // Запланированное время
    uint8_t priority;                   // Приоритет (1-5)
    uint8_t estimated_duration;         // Ожидаемая продолжительность (мин)
    char description[32];               // Описание работ
} maintenance_schedule_t;

// =================================================================
// ОСНОВНЫЕ ФУНКЦИИ ПРЕДИКТИВНОГО ОБСЛУЖИВАНИЯ
// =================================================================

// Инициализация системы предиктивного обслуживания
void Predictive_Maintenance_Init(void);

// Сбор данных для анализа
void Collect_Motor_Data(uint8_t motor_id);

// Анализ трендов
trend_analysis_t Analyze_Motor_Trends(uint8_t motor_id);

// Прогнозирование состояния
maintenance_prediction_t Predict_Motor_Maintenance(uint8_t motor_id);

// Планирование обслуживания
void Schedule_Maintenance(uint8_t motor_id, maintenance_prediction_t* prediction);

// =================================================================
// ФУНКЦИИ АНАЛИЗА И ДИАГНОСТИКИ
// =================================================================

// Расчет показателя здоровья мотора
uint8_t Calculate_Motor_Health_Score(uint8_t motor_id);

// Анализ деградации производительности
uint8_t Analyze_Performance_Degradation(uint8_t motor_id);

// Обнаружение аномалий
uint8_t Detect_Motor_Anomalies(uint8_t motor_id);

// Прогнозирование времени до отказа
uint16_t Predict_Time_To_Failure(uint8_t motor_id);

// =================================================================
// ФУНКЦИИ ОТЧЕТНОСТИ И УВЕДОМЛЕНИЙ
// =================================================================

// Генерация отчета о состоянии
void Generate_Health_Report(uint8_t motor_id);

// Отправка предупреждений
void Send_Maintenance_Alert(uint8_t motor_id, uint8_t alert_type);

// Показать прогноз обслуживания
void Show_Maintenance_Forecast(void);

// Показать расписание обслуживания
void Show_Maintenance_Schedule(void);

// =================================================================
// ФУНКЦИИ УПРАВЛЕНИЯ ДАННЫМИ
// =================================================================

// Сохранение исторических данных
void Save_Historical_Data(uint8_t motor_id, trend_data_point_t* data);

// Загрузка исторических данных
uint8_t Load_Historical_Data(uint8_t motor_id, trend_data_point_t* data, uint8_t count);

// Очистка старых данных
void Cleanup_Old_Data(uint32_t older_than_timestamp);

// =================================================================
// ВСПОМОГАТЕЛЬНЫЕ ФУНКЦИИ
// =================================================================

// Расчет тренда по массиву данных
int8_t Calculate_Trend(uint16_t* values, uint8_t count);

// Интерполяция значений
uint16_t Interpolate_Value(uint16_t value1, uint16_t value2, uint8_t factor);

// Нормализация показателей
uint8_t Normalize_Score(uint16_t value, uint16_t min_val, uint16_t max_val);

// Получение описания состояния
const char* Get_Health_Status_Description(uint8_t health_score);

// Получение рекомендации по обслуживанию
const char* Get_Maintenance_Recommendation(uint8_t motor_id, uint8_t health_score);

#endif // MOTOR_PREDICTIVE_MAINTENANCE_H
