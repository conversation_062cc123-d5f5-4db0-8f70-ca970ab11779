Dependencies for Project 'Servo', Target 'Target 1': (DO NOT MODIFY !)
CompilerVersion: 6230000::V6.23::ARMCLANG
F (.\main.c)(0x684DD1C6)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m3 -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O0 -ffunction-sections -Wall -Wextra -Wno-packed -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier

-I./RTE/Device/STM32F103ZE

-I./RTE/_Target_1

-IC:/Keil_v5/ARM/PACK/ARM/CMSIS/6.1.0/CMSIS/Core/Include

-IC:/Keil_v5/ARM/PACK/Keil/STM32F1xx_DFP/2.4.1/Device/Include

-D__UVISION_VERSION="542" -DSTM32F10X_HD -D_RTE_

-o ./objects/main.o -MMD)
I (main.h)(0x684491D9)
I (stm32f10x.h)(0x684444C8)
I (IO_gpio.h)(0x684429B3)
I (Timers.h)(0x67E66ED4)
I (Rcc.h)(0x66D98E0B)
I (UserFunction.h)(0x684D8106)
I (lcd.h)(0x6724C497)
I (I2C.h)(0x6724BC14)
I (motor_config.h)(0x68445FF7)
I (json_parser.h)(0x68487D83)
I (motor_unified_config.h)(0x684B873A)
I (motor_autocalibration.h)(0x684D3D3F)
F (.\main.h)(0x684491D9)()
F (.\UserFunction.c)(0x684E7795)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m3 -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O0 -ffunction-sections -Wall -Wextra -Wno-packed -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier

-I./RTE/Device/STM32F103ZE

-I./RTE/_Target_1

-IC:/Keil_v5/ARM/PACK/ARM/CMSIS/6.1.0/CMSIS/Core/Include

-IC:/Keil_v5/ARM/PACK/Keil/STM32F1xx_DFP/2.4.1/Device/Include

-D__UVISION_VERSION="542" -DSTM32F10X_HD -D_RTE_

-o ./objects/userfunction.o -MMD)
I (main.h)(0x684491D9)
I (stm32f10x.h)(0x684444C8)
I (IO_gpio.h)(0x684429B3)
I (Timers.h)(0x67E66ED4)
I (Rcc.h)(0x66D98E0B)
I (UserFunction.h)(0x684D8106)
I (lcd.h)(0x6724C497)
I (I2C.h)(0x6724BC14)
I (motor_config.h)(0x68445FF7)
I (json_parser.h)(0x68487D83)
F (.\UserFunction.h)(0x684D8106)()
F (.\json_parser.c)(0x68487D83)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m3 -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O0 -ffunction-sections -Wall -Wextra -Wno-packed -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier

-I./RTE/Device/STM32F103ZE

-I./RTE/_Target_1

-IC:/Keil_v5/ARM/PACK/ARM/CMSIS/6.1.0/CMSIS/Core/Include

-IC:/Keil_v5/ARM/PACK/Keil/STM32F1xx_DFP/2.4.1/Device/Include

-D__UVISION_VERSION="542" -DSTM32F10X_HD -D_RTE_

-o ./objects/json_parser.o -MMD)
I (json_parser.h)(0x68487D83)
I (main.h)(0x684491D9)
I (stm32f10x.h)(0x684444C8)
I (IO_gpio.h)(0x684429B3)
I (Timers.h)(0x67E66ED4)
I (Rcc.h)(0x66D98E0B)
I (UserFunction.h)(0x684D8106)
I (lcd.h)(0x6724C497)
I (I2C.h)(0x6724BC14)
I (motor_config.h)(0x68445FF7)
F (.\motor_autocalibration.c)(0x684E7706)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m3 -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O0 -ffunction-sections -Wall -Wextra -Wno-packed -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier

-I./RTE/Device/STM32F103ZE

-I./RTE/_Target_1

-IC:/Keil_v5/ARM/PACK/ARM/CMSIS/6.1.0/CMSIS/Core/Include

-IC:/Keil_v5/ARM/PACK/Keil/STM32F1xx_DFP/2.4.1/Device/Include

-D__UVISION_VERSION="542" -DSTM32F10X_HD -D_RTE_

-o ./objects/motor_autocalibration.o -MMD)
I (motor_autocalibration.h)(0x684D3D3F)
I (motor_unified_config.h)(0x684B873A)
I (UserFunction.h)(0x684D8106)
I (stm32f10x.h)(0x684444C8)
I (lcd.h)(0x6724C497)
I (I2C.h)(0x6724BC14)
I (IO_gpio.h)(0x684429B3)
I (Timers.h)(0x67E66ED4)
F (.\motor_unified_config.c)(0x684B8762)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m3 -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O0 -ffunction-sections -Wall -Wextra -Wno-packed -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier

-I./RTE/Device/STM32F103ZE

-I./RTE/_Target_1

-IC:/Keil_v5/ARM/PACK/ARM/CMSIS/6.1.0/CMSIS/Core/Include

-IC:/Keil_v5/ARM/PACK/Keil/STM32F1xx_DFP/2.4.1/Device/Include

-D__UVISION_VERSION="542" -DSTM32F10X_HD -D_RTE_

-o ./objects/motor_unified_config.o -MMD)
I (motor_unified_config.h)(0x684B873A)
I (UserFunction.h)(0x684D8106)
I (stm32f10x.h)(0x684444C8)
I (lcd.h)(0x6724C497)
I (I2C.h)(0x6724BC14)
I (main.h)(0x684491D9)
I (IO_gpio.h)(0x684429B3)
I (Timers.h)(0x67E66ED4)
I (Rcc.h)(0x66D98E0B)
I (motor_config.h)(0x68445FF7)
I (json_parser.h)(0x68487D83)
F (.\IO_gpio.c)(0x67BC4375)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m3 -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O0 -ffunction-sections -Wall -Wextra -Wno-packed -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier

-I./RTE/Device/STM32F103ZE

-I./RTE/_Target_1

-IC:/Keil_v5/ARM/PACK/ARM/CMSIS/6.1.0/CMSIS/Core/Include

-IC:/Keil_v5/ARM/PACK/Keil/STM32F1xx_DFP/2.4.1/Device/Include

-D__UVISION_VERSION="542" -DSTM32F10X_HD -D_RTE_

-o ./objects/io_gpio.o -MMD)
I (IO_gpio.h)(0x684429B3)
I (stm32f10x.h)(0x684444C8)
F (.\IO_gpio.h)(0x684429B3)()
F (.\Rcc.c)(0x67E66ED4)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m3 -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O0 -ffunction-sections -Wall -Wextra -Wno-packed -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier

-I./RTE/Device/STM32F103ZE

-I./RTE/_Target_1

-IC:/Keil_v5/ARM/PACK/ARM/CMSIS/6.1.0/CMSIS/Core/Include

-IC:/Keil_v5/ARM/PACK/Keil/STM32F1xx_DFP/2.4.1/Device/Include

-D__UVISION_VERSION="542" -DSTM32F10X_HD -D_RTE_

-o ./objects/rcc.o -MMD)
I (main.h)(0x684491D9)
I (stm32f10x.h)(0x684444C8)
I (IO_gpio.h)(0x684429B3)
I (Timers.h)(0x67E66ED4)
I (Rcc.h)(0x66D98E0B)
I (UserFunction.h)(0x684D8106)
I (lcd.h)(0x6724C497)
I (I2C.h)(0x6724BC14)
I (motor_config.h)(0x68445FF7)
I (json_parser.h)(0x68487D83)
F (.\Rcc.h)(0x66D98E0B)()
F (.\Timers.c)(0x67E687C1)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m3 -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O0 -ffunction-sections -Wall -Wextra -Wno-packed -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier

-I./RTE/Device/STM32F103ZE

-I./RTE/_Target_1

-IC:/Keil_v5/ARM/PACK/ARM/CMSIS/6.1.0/CMSIS/Core/Include

-IC:/Keil_v5/ARM/PACK/Keil/STM32F1xx_DFP/2.4.1/Device/Include

-D__UVISION_VERSION="542" -DSTM32F10X_HD -D_RTE_

-o ./objects/timers.o -MMD)
I (main.h)(0x684491D9)
I (stm32f10x.h)(0x684444C8)
I (IO_gpio.h)(0x684429B3)
I (Timers.h)(0x67E66ED4)
I (Rcc.h)(0x66D98E0B)
I (UserFunction.h)(0x684D8106)
I (lcd.h)(0x6724C497)
I (I2C.h)(0x6724BC14)
I (motor_config.h)(0x68445FF7)
I (json_parser.h)(0x68487D83)
F (.\Timers.h)(0x67E66ED4)()
F (.\I2C.c)(0x6724BC14)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m3 -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O0 -ffunction-sections -Wall -Wextra -Wno-packed -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier

-I./RTE/Device/STM32F103ZE

-I./RTE/_Target_1

-IC:/Keil_v5/ARM/PACK/ARM/CMSIS/6.1.0/CMSIS/Core/Include

-IC:/Keil_v5/ARM/PACK/Keil/STM32F1xx_DFP/2.4.1/Device/Include

-D__UVISION_VERSION="542" -DSTM32F10X_HD -D_RTE_

-o ./objects/i2c.o -MMD)
I (stm32f10x.h)(0x684444C8)
I (main.h)(0x684491D9)
I (IO_gpio.h)(0x684429B3)
I (Timers.h)(0x67E66ED4)
I (Rcc.h)(0x66D98E0B)
I (UserFunction.h)(0x684D8106)
I (lcd.h)(0x6724C497)
I (I2C.h)(0x6724BC14)
I (motor_config.h)(0x68445FF7)
I (json_parser.h)(0x68487D83)
F (.\I2C.h)(0x6724BC14)()
F (.\lcd.c)(0x6724CBE1)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m3 -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O0 -ffunction-sections -Wall -Wextra -Wno-packed -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier

-I./RTE/Device/STM32F103ZE

-I./RTE/_Target_1

-IC:/Keil_v5/ARM/PACK/ARM/CMSIS/6.1.0/CMSIS/Core/Include

-IC:/Keil_v5/ARM/PACK/Keil/STM32F1xx_DFP/2.4.1/Device/Include

-D__UVISION_VERSION="542" -DSTM32F10X_HD -D_RTE_

-o ./objects/lcd.o -MMD)
I (stm32f10x.h)(0x684444C8)
I (main.h)(0x684491D9)
I (IO_gpio.h)(0x684429B3)
I (Timers.h)(0x67E66ED4)
I (Rcc.h)(0x66D98E0B)
I (UserFunction.h)(0x684D8106)
I (lcd.h)(0x6724C497)
I (I2C.h)(0x6724BC14)
I (motor_config.h)(0x68445FF7)
I (json_parser.h)(0x68487D83)
F (.\lcd.h)(0x6724C497)()
F (RTE/Device/STM32F103ZE/RTE_Device.h)(0x6838E91C)()
F (RTE/Device/STM32F103ZE/startup_config.h)(0x68396D10)()
F (RTE/Device/STM32F103ZE/startup_stm32f10x_hd.s)(0x6838E91B)(--target=arm-arm-none-eabi -mcpu=cortex-m3 -masm=auto  -Wa,armasm,--diag_suppress=A1950W -c

-gdwarf-4

-I./RTE/Device/STM32F103ZE

-I./RTE/_Target_1

-IC:/Keil_v5/ARM/PACK/ARM/CMSIS/6.1.0/CMSIS/Core/Include

-IC:/Keil_v5/ARM/PACK/Keil/STM32F1xx_DFP/2.4.1/Device/Include

-Wa,armasm,--pd,"__UVISION_VERSION SETA 542" -Wa,armasm,--pd,"STM32F10X_HD SETA 1" -Wa,armasm,--pd,"_RTE_ SETA 1"

-o ./objects/startup_stm32f10x_hd.o)
F (RTE/Device/STM32F103ZE/system_stm32f10x.c)(0x6838E91B)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m3 -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O0 -ffunction-sections -Wall -Wextra -Wno-packed -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier

-I./RTE/Device/STM32F103ZE

-I./RTE/_Target_1

-IC:/Keil_v5/ARM/PACK/ARM/CMSIS/6.1.0/CMSIS/Core/Include

-IC:/Keil_v5/ARM/PACK/Keil/STM32F1xx_DFP/2.4.1/Device/Include

-D__UVISION_VERSION="542" -DSTM32F10X_HD -D_RTE_

-o ./objects/system_stm32f10x.o -MMD)
I (C:\Keil_v5\ARM\PACK\Keil\STM32F1xx_DFP\2.4.1\Device\Include\stm32f10x.h)(0x680EE1F0)
I (RTE\_Target_1\RTE_Components.h)(0x68442B61)
I (C:\Keil_v5\ARM\PACK\ARM\CMSIS\6.1.0\CMSIS\Core\Include\core_cm3.h)(0x680EE1BF)
I (C:\Keil_v5\ARM\PACK\Keil\STM32F1xx_DFP\2.4.1\Device\Include\system_stm32f10x.h)(0x680EE1F0)
