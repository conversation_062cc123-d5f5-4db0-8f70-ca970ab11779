#ifndef MAIN_H
#define MAIN_H

#endif

#include "stdint.h"


//#define SLAVE_ADDRESS_R 0xD0 //LSB == 0
//#define SLAVE_ADDRESS_Transmit 0xD0 //From Master to Slave
#define SLAVE_ADDRESS_Transmit 0x27 //LCD 2004 Address

//#define SLAVE_ADDRESS_W 0xD1 //LSB == 1
#define SLAVE_ADDRESS_Receive 0xD1 //From Slave to Master

extern uint16_t I2C_DATA;

extern uint8_t BackLight;
//Stop I2C:

#define I2C1_START   I2C1 -> CR1 |=  I2C_CR1_START
#define I2C1_STOP    I2C1 -> CR1 |= I2C_CR1_STOP 

void SetUp_I2C1(void);

void I2C1_Start(void);

void I2C1_AddrSend_Transmit(void);

void I2C1_AddrSend_Receive(void);

