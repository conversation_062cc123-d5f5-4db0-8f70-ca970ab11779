#ifndef SD_CARD_H
#define SD_CARD_H

#include <stdint.h>

// Результаты операций с SD картой
typedef enum {
    SD_OK = 0,
    SD_ERROR,
    SD_NOT_READY,
    SD_NOT_PRESENT,
    SD_WRITE_PROTECTED,
    SD_INVALID_PARAMETER
} SD_Result_t;

// Информация о файле
typedef struct {
    char name[32];
    uint32_t size;
    uint32_t date;
    uint8_t attributes;
} SD_FileInfo_t;

// Функции для работы с SD картой
SD_Result_t SD_Init(void);
SD_Result_t SD_DeInit(void);
uint8_t SD_IsPresent(void);
uint8_t SD_IsWriteProtected(void);

// Функции для работы с файлами
SD_Result_t SD_OpenFile(const char* filename, uint8_t mode);
SD_Result_t SD_CloseFile(void);
SD_Result_t SD_ReadFile(uint8_t* buffer, uint32_t size, uint32_t* bytes_read);
SD_Result_t SD_WriteFile(const uint8_t* buffer, uint32_t size, uint32_t* bytes_written);
SD_Result_t SD_SeekFile(uint32_t position);
SD_Result_t SD_GetFileSize(uint32_t* size);

// Функции для работы с директориями
SD_Result_t SD_ListFiles(SD_FileInfo_t* files, uint32_t max_files, uint32_t* found_files);
SD_Result_t SD_DeleteFile(const char* filename);
SD_Result_t SD_CreateFile(const char* filename);

// Режимы открытия файлов
#define SD_MODE_READ        0x01
#define SD_MODE_WRITE       0x02
#define SD_MODE_CREATE      0x04
#define SD_MODE_APPEND      0x08

// Атрибуты файлов
#define SD_ATTR_READONLY    0x01
#define SD_ATTR_HIDDEN      0x02
#define SD_ATTR_SYSTEM      0x04
#define SD_ATTR_DIRECTORY   0x10
#define SD_ATTR_ARCHIVE     0x20

// Функции высокого уровня для работы с конфигурацией
SD_Result_t SD_LoadMotorConfig(void* config, uint32_t size);
SD_Result_t SD_SaveMotorConfig(const void* config, uint32_t size);
SD_Result_t SD_LoadMotorConfigTXT(void* config, uint32_t size);
SD_Result_t SD_SaveMotorConfigTXT(const void* config, uint32_t size);

// Функция для создания примера конфигурационного файла
SD_Result_t SD_CreateSampleConfigTXT(void);

#endif /* SD_CARD_H */
