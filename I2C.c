#ifndef MAIN_H
#define MAIN_H

#endif

#include "stm32f10x.h"

#include "main.h"
#include "I2C.h"

void SetUp_I2C1(void)
 {
	 I2C1 -> CR1 &=  ~I2C_CR1_PE; //Disable
	 
	 I2C1 -> CR2 &= ~ I2C_CR2_FREQ;
	 //I2C1 -> CR2 |= 2;
	 I2C1 -> CR2 |= 36;
	 
	 I2C1 -> CCR &= ~I2C_CCR_CCR;
	 //I2C1 -> CCR  |= 10;
	 I2C1 -> CCR  |= 179;
	 
	 //I2C1 -> TRISE = 3; //Maximum rise time in Fm/Sm mode (Master mode)
	 I2C1 -> TRISE = 37;
	 I2C1 -> CR1 |=  I2C_CR1_PE; //Enable
 }
 
 void I2C1_Start(void)
  {
	 I2C1 -> CR1 |=  I2C_CR1_START;	
   
		//Waite for EV5 (Set up bit SB):
   while(!(I2C1 -> SR1 & I2C_SR1_SB))
	  {
			
	  }		 
  }
	
	//Slave Address with Read Send:
//void I2C1_AddrSend_R(void)
void I2C1_AddrSend_Transmit(void)	
 {
	 (void) I2C1 -> SR1; //to clear bit SB
	 
	 //I2C1 -> DR = SLAVE_ADDRESS_R; 
	  I2C1 -> DR = SLAVE_ADDRESS_Transmit;
	 
	 //Wait for End of EV6 transmit:
	 while(!(I2C1 -> SR1 & I2C_SR1_ADDR))
	  {
	  }
		//Clear bit ADDR:
	 (void) I2C1 -> SR1;
	 (void) I2C1 -> SR2;
 }
 
	//Slave Address with Write Send:
//void I2C1_AddrSend_W(void)
void I2C1_AddrSend_Receive(void) 
 {
	 (void) I2C1 -> SR1; //to clear bit SB
	 
	 //I2C1 -> DR = SLAVE_ADDRESS_W; 
	 I2C1 -> DR = SLAVE_ADDRESS_Receive;
	 
	 //Wait for End of EV6 transmit:
	 while(!(I2C1 -> SR1 & I2C_SR1_ADDR))
	  {
	  }
		//Clear bit ADDR:
	 (void) I2C1 -> SR1;
	 (void) I2C1 -> SR2;
 }

 