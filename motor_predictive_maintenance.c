#include "motor_predictive_maintenance.h"
#include "motor_unified_config.h"
#include "UserFunction.h"
#include "IO_gpio.h"
#include "lcd.h"
#include "Timers.h"
#include <string.h>
#include <stddef.h>

// Определение NULL если не определено
#ifndef NULL
#define NULL ((void*)0)
#endif

// Объявления внешних функций
extern void Delay_mS(uint8_t Delay);
extern uint32_t Get_System_MS(void);

// =================================================================
// ГЛОБАЛЬНЫЕ ПЕРЕМЕННЫЕ
// =================================================================

// Исторические данные для анализа трендов
static trend_data_point_t trend_history[MAX_MOTORS][TREND_ANALYSIS_POINTS];
static uint8_t trend_history_index[MAX_MOTORS];

// Прогнозы обслуживания для всех моторов
static maintenance_prediction_t maintenance_predictions[MAX_MOTORS];

// Расписание обслуживания
static maintenance_schedule_t maintenance_schedule[MAX_MOTORS * 3]; // До 3 задач на мотор
static uint8_t schedule_count = 0;

// Флаг инициализации системы
static uint8_t predictive_maintenance_initialized = 0;

// =================================================================
// ИНИЦИАЛИЗАЦИЯ СИСТЕМЫ ПРЕДИКТИВНОГО ОБСЛУЖИВАНИЯ
// =================================================================

void Predictive_Maintenance_Init(void) {
    // Очистка исторических данных
    for(uint8_t motor = 0; motor < MAX_MOTORS; motor++) {
        trend_history_index[motor] = 0;
        
        for(uint8_t i = 0; i < TREND_ANALYSIS_POINTS; i++) {
            trend_history[motor][i].timestamp = 0;
            trend_history[motor][i].success_rate = 100;
            trend_history[motor][i].avg_response_time = 1000;
            trend_history[motor][i].temperature = 25;
            trend_history[motor][i].vibration_level = 0;
            trend_history[motor][i].operations_count = 0;
        }
        
        // Инициализация прогнозов
        maintenance_predictions[motor].motor_id = motor;
        maintenance_predictions[motor].current_health_score = 100;
        maintenance_predictions[motor].predicted_health_score = 100;
        maintenance_predictions[motor].hours_to_maintenance = 1000;
        maintenance_predictions[motor].hours_to_failure = 5000;
        maintenance_predictions[motor].maintenance_priority = 1;
        maintenance_predictions[motor].confidence_level = 50;
        strcpy(maintenance_predictions[motor].recommendation, "Normal operation");
    }
    
    // Очистка расписания
    schedule_count = 0;
    
    predictive_maintenance_initialized = 1;
    
    LCD_Send_Command(LCD_4_LINE_POS_0);
    LCD_SendString((uint8_t *)"Predictive: Ready   ", 20);
    Delay_mS(500);
}

// =================================================================
// СБОР ДАННЫХ ДЛЯ АНАЛИЗА
// =================================================================

void Collect_Motor_Data(uint8_t motor_id) {
    if(!predictive_maintenance_initialized) {
        Predictive_Maintenance_Init();
    }
    
    if(motor_id >= MAX_MOTORS) return;
    
    // Получаем текущую статистику мотора
    motor_stats_t* stats = Get_Motor_Stats(motor_id);
    if(!stats) return;
    
    // Создаем новую точку данных
    trend_data_point_t new_point;
    new_point.timestamp = Get_System_MS();
    
    // Расчет показателей
    if(stats->total_steps > 0) {
        new_point.success_rate = (stats->successful_moves * 100) / stats->total_steps;
    } else {
        new_point.success_rate = 100;
    }
    
    new_point.avg_response_time = stats->last_move_time;
    new_point.temperature = stats->temperature;
    new_point.vibration_level = (stats->errors * 10); // Простая оценка вибрации
    new_point.operations_count = stats->total_steps;
    
    // Сохраняем в историю
    uint8_t index = trend_history_index[motor_id];
    trend_history[motor_id][index] = new_point;
    
    // Обновляем индекс (циклический буфер)
    trend_history_index[motor_id] = (index + 1) % TREND_ANALYSIS_POINTS;
}

// =================================================================
// АНАЛИЗ ТРЕНДОВ
// =================================================================

trend_analysis_t Analyze_Motor_Trends(uint8_t motor_id) {
    trend_analysis_t result;
    result.success_rate_trend = 0;
    result.response_time_trend = 0;
    result.temperature_trend = 0;
    result.vibration_trend = 0;
    result.trend_confidence = 0;
    
    if(motor_id >= MAX_MOTORS) return result;
    
    // Собираем данные для анализа
    uint16_t success_rates[TREND_ANALYSIS_POINTS];
    uint16_t response_times[TREND_ANALYSIS_POINTS];
    uint16_t temperatures[TREND_ANALYSIS_POINTS];
    uint16_t vibrations[TREND_ANALYSIS_POINTS];
    
    uint8_t valid_points = 0;
    
    for(uint8_t i = 0; i < TREND_ANALYSIS_POINTS; i++) {
        if(trend_history[motor_id][i].timestamp > 0) {
            success_rates[valid_points] = trend_history[motor_id][i].success_rate;
            response_times[valid_points] = trend_history[motor_id][i].avg_response_time;
            temperatures[valid_points] = trend_history[motor_id][i].temperature;
            vibrations[valid_points] = trend_history[motor_id][i].vibration_level;
            valid_points++;
        }
    }
    
    // Анализ трендов (если достаточно данных)
    if(valid_points >= 3) {
        result.success_rate_trend = Calculate_Trend(success_rates, valid_points);
        result.response_time_trend = Calculate_Trend(response_times, valid_points);
        result.temperature_trend = Calculate_Trend(temperatures, valid_points);
        result.vibration_trend = Calculate_Trend(vibrations, valid_points);
        
        // Уверенность зависит от количества точек данных
        result.trend_confidence = (valid_points * 100) / TREND_ANALYSIS_POINTS;
    }
    
    return result;
}

// =================================================================
// ПРОГНОЗИРОВАНИЕ СОСТОЯНИЯ
// =================================================================

maintenance_prediction_t Predict_Motor_Maintenance(uint8_t motor_id) {
    maintenance_prediction_t prediction = maintenance_predictions[motor_id];
    
    if(motor_id >= MAX_MOTORS) return prediction;
    
    // Сначала собираем свежие данные
    Collect_Motor_Data(motor_id);
    
    // Анализируем тренды
    trend_analysis_t trends = Analyze_Motor_Trends(motor_id);
    
    // Расчет текущего показателя здоровья
    prediction.current_health_score = Calculate_Motor_Health_Score(motor_id);
    
    // Прогнозирование будущего состояния на основе трендов
    int16_t health_change = 0;
    
    // Влияние тренда успешности
    health_change += (trends.success_rate_trend * WEIGHT_SUCCESS_RATE) / 100;
    
    // Влияние тренда времени отклика (отрицательное влияние при росте времени)
    health_change -= (trends.response_time_trend * WEIGHT_RESPONSE_TIME) / 100;
    
    // Влияние тренда температуры (отрицательное влияние при росте)
    health_change -= (trends.temperature_trend * WEIGHT_TEMPERATURE) / 100;
    
    // Влияние тренда вибрации (отрицательное влияние при росте)
    health_change -= (trends.vibration_trend * WEIGHT_VIBRATION) / 100;
    
    // Прогнозируемое здоровье
    int16_t predicted_health = prediction.current_health_score + health_change;
    if(predicted_health < 0) predicted_health = 0;
    if(predicted_health > 100) predicted_health = 100;
    prediction.predicted_health_score = predicted_health;
    
    // Расчет времени до обслуживания
    if(predicted_health < MAINTENANCE_WARNING_THRESHOLD) {
        prediction.hours_to_maintenance = 24; // Срочное обслуживание
        prediction.maintenance_priority = 4;
        strcpy(prediction.recommendation, "Urgent maintenance");
    } else if(predicted_health < MAINTENANCE_GOOD_THRESHOLD) {
        prediction.hours_to_maintenance = 168; // Неделя
        prediction.maintenance_priority = 3;
        strcpy(prediction.recommendation, "Schedule maintenance");
    } else {
        prediction.hours_to_maintenance = 720; // Месяц
        prediction.maintenance_priority = 1;
        strcpy(prediction.recommendation, "Normal operation");
    }
    
    // Расчет времени до возможного отказа
    if(health_change < 0) {
        // Экстраполяция: сколько времени до критического состояния
        uint16_t health_margin = predicted_health - MAINTENANCE_CRITICAL_THRESHOLD;
        if(health_change < -1) {
            prediction.hours_to_failure = (health_margin * 24) / (-health_change);
        } else {
            prediction.hours_to_failure = health_margin * 100; // Очень медленная деградация
        }
    } else {
        prediction.hours_to_failure = 8760; // Год (улучшение состояния)
    }
    
    // Уверенность прогноза
    prediction.confidence_level = trends.trend_confidence;
    
    // Сохраняем прогноз
    maintenance_predictions[motor_id] = prediction;
    
    return prediction;
}

// =================================================================
// РАСЧЕТ ПОКАЗАТЕЛЯ ЗДОРОВЬЯ МОТОРА
// =================================================================

uint8_t Calculate_Motor_Health_Score(uint8_t motor_id) {
    if(motor_id >= MAX_MOTORS) return 0;
    
    motor_stats_t* stats = Get_Motor_Stats(motor_id);
    if(!stats) return 0;
    
    uint16_t health_score = 100;
    
    // Фактор успешности операций
    if(stats->total_steps > 0) {
        uint8_t success_rate = (stats->successful_moves * 100) / stats->total_steps;
        if(success_rate < 90) {
            health_score -= (90 - success_rate) * 2; // Штраф за низкую успешность
        }
    }
    
    // Фактор количества ошибок
    if(stats->errors > 0) {
        health_score -= stats->errors * 5; // Штраф за ошибки
    }
    
    // Фактор таймаутов
    if(stats->timeouts > 0) {
        health_score -= stats->timeouts * 10; // Большой штраф за таймауты
    }
    
    // Фактор температуры
    if(stats->temperature > 40) {
        health_score -= (stats->temperature - 40) * 2; // Штраф за перегрев
    }
    
    // Ограничиваем результат
    if(health_score > 100) health_score = 100;
    if(health_score < 0) health_score = 0;
    
    return (uint8_t)health_score;
}

// =================================================================
// ФУНКЦИИ ОТЧЕТНОСТИ И УВЕДОМЛЕНИЙ
// =================================================================

void Generate_Health_Report(uint8_t motor_id) {
    if(motor_id >= MAX_MOTORS) return;

    maintenance_prediction_t prediction = Predict_Motor_Maintenance(motor_id);

    LCD_Send_Command(LCD_1_LINE_POS_0);
    LCD_SendString((uint8_t *)"=== HEALTH REPORT ===", 20);

    LCD_Send_Command(LCD_2_LINE_POS_0);
    LCD_SendString((uint8_t *)"Health: ", 8);
    if(prediction.current_health_score >= 80) {
        LCD_SendString((uint8_t *)"EXCELLENT   ", 12);
    } else if(prediction.current_health_score >= 60) {
        LCD_SendString((uint8_t *)"GOOD        ", 12);
    } else if(prediction.current_health_score >= 40) {
        LCD_SendString((uint8_t *)"WARNING     ", 12);
    } else {
        LCD_SendString((uint8_t *)"CRITICAL    ", 12);
    }

    LCD_Send_Command(LCD_3_LINE_POS_0);
    LCD_SendString((uint8_t *)prediction.recommendation, 20);

    LCD_Send_Command(LCD_4_LINE_POS_0);
    if(prediction.hours_to_maintenance < 48) {
        LCD_SendString((uint8_t *)"Maintenance: URGENT ", 20);
    } else if(prediction.hours_to_maintenance < 168) {
        LCD_SendString((uint8_t *)"Maintenance: SOON   ", 20);
    } else {
        LCD_SendString((uint8_t *)"Maintenance: OK     ", 20);
    }

    Delay_mS(250);
    Delay_mS(250);
    Delay_mS(250);
    Delay_mS(250); // 1 секунда
}

void Send_Maintenance_Alert(uint8_t motor_id, uint8_t alert_type) {
    LCD_Send_Command(LCD_1_LINE_POS_0);
    LCD_SendString((uint8_t *)"=== ALERT ===       ", 20);

    LCD_Send_Command(LCD_2_LINE_POS_0);
    switch(alert_type) {
        case MAINTENANCE_STATUS_CRITICAL:
            LCD_SendString((uint8_t *)"CRITICAL: Service   ", 20);
            // Звуковой сигнал тревоги
            for(uint8_t i = 0; i < 5; i++) {
                BEEP_ON;
                Delay_mS(200);
                BEEP_OFF;
                Delay_mS(200);
            }
            break;

        case MAINTENANCE_STATUS_WARNING:
            LCD_SendString((uint8_t *)"WARNING: Check soon ", 20);
            BEEP_ON;
            Delay_mS(500);
            BEEP_OFF;
            break;

        default:
            LCD_SendString((uint8_t *)"INFO: Schedule maint", 20);
            break;
    }

    LCD_Send_Command(LCD_3_LINE_POS_0);
    LCD_SendString((uint8_t *)"Motor needs attention", 20);

    LCD_Send_Command(LCD_4_LINE_POS_0);
    LCD_SendString((uint8_t *)"Press any key...    ", 20);

    Delay_mS(250);
    Delay_mS(250);
    Delay_mS(250);
    Delay_mS(250); // 1 секунда
}

void Show_Maintenance_Forecast(void) {
    LCD_Send_Command(LCD_1_LINE_POS_0);
    LCD_SendString((uint8_t *)"=== FORECAST ===    ", 20);

    // Показываем прогноз для критичных моторов
    uint8_t critical_motors[] = {2, 3, 4}; // M2, M3, M4

    for(uint8_t i = 0; i < 3; i++) {
        uint8_t motor_id = critical_motors[i];
        maintenance_prediction_t prediction = Predict_Motor_Maintenance(motor_id);

        LCD_Send_Command(LCD_2_LINE_POS_0 + i);

        if(prediction.current_health_score >= 80) {
            LCD_SendString((uint8_t *)"M2/3/4: All OK      ", 20);
            break;
        } else if(prediction.current_health_score >= 60) {
            LCD_SendString((uint8_t *)"M2/3/4: Good        ", 20);
            break;
        } else {
            LCD_SendString((uint8_t *)"M2/3/4: Need service", 20);
            break;
        }
    }

    Delay_mS(250);
    Delay_mS(250);
    Delay_mS(250);
    Delay_mS(250); // 1 секунда
}

// =================================================================
// ВСПОМОГАТЕЛЬНЫЕ ФУНКЦИИ
// =================================================================

int8_t Calculate_Trend(uint16_t* values, uint8_t count) {
    if(count < 2) return 0;

    // Простой расчет тренда: сравнение первой и последней трети
    uint8_t third = count / 3;
    if(third < 1) third = 1;

    uint32_t first_avg = 0;
    uint32_t last_avg = 0;

    // Среднее первой трети
    for(uint8_t i = 0; i < third; i++) {
        first_avg += values[i];
    }
    first_avg /= third;

    // Среднее последней трети
    for(uint8_t i = count - third; i < count; i++) {
        last_avg += values[i];
    }
    last_avg /= third;

    // Расчет тренда в процентах
    if(first_avg == 0) return 0;

    int32_t trend = ((int32_t)last_avg - (int32_t)first_avg) * 100 / (int32_t)first_avg;

    // Ограничиваем результат
    if(trend > 100) trend = 100;
    if(trend < -100) trend = -100;

    return (int8_t)trend;
}

const char* Get_Health_Status_Description(uint8_t health_score) {
    if(health_score >= 80) return "EXCELLENT";
    if(health_score >= 60) return "GOOD";
    if(health_score >= 40) return "WARNING";
    if(health_score >= 20) return "CRITICAL";
    return "FAILURE";
}

const char* Get_Maintenance_Recommendation(uint8_t motor_id, uint8_t health_score) {
    if(health_score >= 80) return "Normal operation";
    if(health_score >= 60) return "Monitor closely";
    if(health_score >= 40) return "Schedule maintenance";
    if(health_score >= 20) return "Urgent maintenance";
    return "Replace immediately";
}
