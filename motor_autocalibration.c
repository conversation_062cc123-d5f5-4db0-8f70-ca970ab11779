#include "motor_autocalibration.h"
#include "motor_unified_config.h"
#include "UserFunction.h"
#include "IO_gpio.h"
#include "lcd.h"
#include "Timers.h"
#include <string.h>
#include <stddef.h>

// Определение NULL если не определено
#ifndef NULL
#define NULL ((void*)0)
#endif

// Объявления внешних функций
extern void Delay_mS(uint8_t Delay);
extern void Delay_uS(uint16_t Delay);
extern uint32_t Get_System_MS(void);

// =================================================================
// ГЛОБАЛЬНЫЕ ПЕРЕМЕННЫЕ
// =================================================================

// Результаты калибровки всех моторов
static motor_calibration_result_t calibration_results[MAX_MOTORS];

// Флаг инициализации системы
static uint8_t autocalibration_initialized = 0;

// =================================================================
// ИНИЦИАЛИЗАЦИЯ СИСТЕМЫ АВТОКАЛИБРОВКИ
// =================================================================

void AutoCalibration_Init(void) {
    // Очистка результатов калибровки
    for(uint8_t i = 0; i < MAX_MOTORS; i++) {
        calibration_results[i].motor_id = i;
        calibration_results[i].calibration_status = CALIBRATION_ERROR_DISABLED;
        calibration_results[i].optimal_delay_us = 0;
        calibration_results[i].safe_delay_us = 0;
        calibration_results[i].max_delay_us = 0;
        calibration_results[i].optimal_success_rate = 0;
        calibration_results[i].total_tests = 0;
        calibration_results[i].calibration_time_ms = 0;
        strcpy(calibration_results[i].status_message, "Not calibrated");
    }
    
    autocalibration_initialized = 1;
    
    LCD_Send_Command(LCD_4_LINE_POS_0);
    LCD_SendString((uint8_t *)"AutoCalib: Ready    ", 20);
    Delay_mS(250);
    Delay_mS(250); // 500мс общая задержка
}

// =================================================================
// КАЛИБРОВКА ОДНОГО МОТОРА
// =================================================================

uint8_t AutoCalibrate_Motor(uint8_t motor_id, motor_calibration_result_t* result) {
    if(!autocalibration_initialized) {
        AutoCalibration_Init();
    }
    
    // Проверка валидности параметров
    if(motor_id >= MAX_MOTORS || result == NULL) {
        return CALIBRATION_ERROR_MOTOR_ID;
    }
    
    // Проверка готовности мотора
    if(!Check_Motor_Ready_For_Calibration(motor_id)) {
        result->calibration_status = CALIBRATION_ERROR_DISABLED;
        strcpy(result->status_message, "Motor disabled");
        return CALIBRATION_ERROR_DISABLED;
    }
    
    // Инициализация результата
    result->motor_id = motor_id;
    result->calibration_status = CALIBRATION_SUCCESS;
    result->total_tests = 0;
    uint32_t start_time = Get_System_MS();
    
    LCD_Send_Command(LCD_1_LINE_POS_0);
    LCD_SendString((uint8_t *)"=== AUTO CALIBRATE ==", 20);
    
    LCD_Send_Command(LCD_2_LINE_POS_0);
    LCD_SendString((uint8_t *)"Motor: Starting...  ", 20);
    
    // Сохранение исходных настроек
    // motor_config_t* motor = Get_Motor_Config(motor_id); // Для будущего использования
    // uint32_t original_delay = motor->step_delay_us; // Сохранено для будущего использования
    
    // ЭТАП 1: Поиск максимальной безопасной скорости
    Show_Calibration_Progress(motor_id, 10);
    uint32_t max_safe_speed = Find_Max_Safe_Speed(motor_id);
    result->max_delay_us = max_safe_speed;
    
    // ЭТАП 2: Бинарный поиск оптимальной скорости
    Show_Calibration_Progress(motor_id, 30);
    uint32_t optimal_speed = Find_Optimal_Speed_Binary(motor_id, CALIBRATION_MIN_DELAY_US, max_safe_speed);
    result->optimal_delay_us = optimal_speed;
    
    // ЭТАП 3: Расчет безопасной скорости (на 20% медленнее оптимальной)
    Show_Calibration_Progress(motor_id, 60);
    result->safe_delay_us = (optimal_speed * 120) / 100; // +20% к задержке = -20% к скорости
    
    // ЭТАП 4: Финальный тест стабильности
    Show_Calibration_Progress(motor_id, 80);
    speed_test_result_t final_test;
    if(Test_Motor_Speed_Detailed(motor_id, optimal_speed, &final_test) == CALIBRATION_SUCCESS) {
        result->optimal_success_rate = final_test.success_rate;
    } else {
        result->calibration_status = CALIBRATION_ERROR_UNSTABLE;
        strcpy(result->status_message, "Unstable at optimal");
    }
    
    // ЭТАП 5: Применение результатов
    Show_Calibration_Progress(motor_id, 90);
    if(result->calibration_status == CALIBRATION_SUCCESS) {
        Apply_Calibration_Results(motor_id, result);
        strcpy(result->status_message, "Success");
    } else {
        // Восстановление исходных настроек при ошибке
        Restore_Original_Settings(motor_id);
    }
    
    // Завершение
    result->calibration_time_ms = Get_System_MS() - start_time;
    Show_Calibration_Progress(motor_id, 100);
    
    // Сохранение результатов
    calibration_results[motor_id] = *result;
    
    // Отображение результатов
    Show_Calibration_Results(result);
    
    return result->calibration_status;
}

// =================================================================
// КАЛИБРОВКА ВСЕХ МОТОРОВ
// =================================================================

void AutoCalibrate_All_Motors(void) {
    LCD_Send_Command(LCD_1_LINE_POS_0);
    LCD_SendString((uint8_t *)"=== CALIBRATE ALL ===", 20);
    
    uint8_t successful_calibrations = 0;
    uint8_t total_motors = 0;
    
    // Калибруем моторы M1-M6 (M7 - DC мотор, не калибруется)
    for(uint8_t motor_id = 1; motor_id <= 6; motor_id++) {
        motor_config_t* motor = Get_Motor_Config(motor_id);
        if(motor && motor->enabled) {
            total_motors++;
            
            motor_calibration_result_t result;
            if(AutoCalibrate_Motor(motor_id, &result) == CALIBRATION_SUCCESS) {
                successful_calibrations++;
            }
            
            // Пауза между калибровками
            Delay_mS(250);
            Delay_mS(250);
            Delay_mS(250);
            Delay_mS(250); // 1000мс общая задержка
        }
    }
    
    // Итоговый отчет
    LCD_Send_Command(LCD_1_LINE_POS_0);
    LCD_SendString((uint8_t *)"=== CALIBRATION DONE =", 20);
    
    LCD_Send_Command(LCD_2_LINE_POS_0);
    LCD_SendString((uint8_t *)"Calibration done    ", 20);
    
    if(successful_calibrations == total_motors) {
        LCD_Send_Command(LCD_3_LINE_POS_0);
        LCD_SendString((uint8_t *)"Status: EXCELLENT   ", 20);
        
        // Звуковой сигнал успеха
        for(uint8_t i = 0; i < 3; i++) {
            BEEP_ON;
            Delay_mS(100);
            BEEP_OFF;
            Delay_mS(100);
        }
    } else {
        LCD_Send_Command(LCD_3_LINE_POS_0);
        LCD_SendString((uint8_t *)"Status: PARTIAL     ", 20);
        
        // Звуковой сигнал предупреждения
        BEEP_ON;
        Delay_mS(250);
        Delay_mS(250); // 500мс общая задержка
        BEEP_OFF;
    }
    
    LCD_Send_Command(LCD_4_LINE_POS_0);
    LCD_SendString((uint8_t *)"Press any key...    ", 20);
    
    // 3 секунды задержки
    for(uint8_t i = 0; i < 12; i++) {
        Delay_mS(250);
    }
}

// =================================================================
// БЫСТРАЯ КАЛИБРОВКА КРИТИЧНЫХ МОТОРОВ
// =================================================================

void AutoCalibrate_Critical_Motors(void) {
    LCD_Send_Command(LCD_1_LINE_POS_0);
    LCD_SendString((uint8_t *)"=== CRITICAL CALIB ===", 20);
    
    // Критичные моторы: M2 (самый медленный), M3, M4 (часто используемые)
    uint8_t critical_motors[] = {2, 3, 4};
    uint8_t num_critical = sizeof(critical_motors) / sizeof(critical_motors[0]);
    
    for(uint8_t i = 0; i < num_critical; i++) {
        uint8_t motor_id = critical_motors[i];
        
        LCD_Send_Command(LCD_2_LINE_POS_0);
        LCD_SendString((uint8_t *)"Calibrating motor...", 20);
        
        motor_calibration_result_t result;
        AutoCalibrate_Motor(motor_id, &result);
        
        Delay_mS(250);
        Delay_mS(250); // 500мс общая задержка
    }
    
    LCD_Send_Command(LCD_1_LINE_POS_0);
    LCD_SendString((uint8_t *)"=== CRITICAL DONE ===", 20);
    // 2 секунды задержки
    for(uint8_t i = 0; i < 8; i++) {
        Delay_mS(250);
    }
}

// =================================================================
// ФУНКЦИИ ТЕСТИРОВАНИЯ СКОРОСТИ
// =================================================================

uint8_t Test_Motor_Speed_Detailed(uint8_t motor_id, uint32_t delay_us, speed_test_result_t* result) {
    if(motor_id >= MAX_MOTORS || result == NULL) {
        return CALIBRATION_ERROR_MOTOR_ID;
    }

    motor_config_t* motor = Get_Motor_Config(motor_id);
    if(!motor || !motor->enabled) {
        return CALIBRATION_ERROR_DISABLED;
    }

    // Инициализация результата
    result->delay_us = delay_us;
    result->success_rate = 0;
    result->avg_step_time_us = 0;
    result->max_step_time_us = 0;
    result->stability_score = 0;
    result->temperature_rise = 0;

    uint8_t successful_steps = 0;
    uint32_t total_step_time = 0;
    uint16_t max_time = 0;

    // Выбор и инициализация мотора
    switch(motor_id) {
        case 1: Choose_M1; break;
        case 2: Choose_M2; break;
        case 3: Choose_M3; break;
        case 4: Choose_M4; break;
        case 5: Choose_M5; break;
        case 6: Choose_M6; break;
        default: return CALIBRATION_ERROR_MOTOR_ID;
    }

    DD16_Enble;
    Delay_mS(5);
    Enable_Motor;
    Rotate_CW; // Тестовое направление

    // Стабилизация
    for(uint16_t t = 0; t < 1000; t++) {}

    // Тестирование заданного количества шагов
    for(uint16_t step = 0; step < CALIBRATION_TEST_STEPS; step++) {
        // Выполнение одного шага (упрощенная версия без точного измерения времени)
        GPIOB->ODR |= GPIO_ODR_ODR0;
        Delay_uS(delay_us);
        GPIOB->ODR &= (~GPIO_ODR_ODR0);
        Delay_uS(delay_us);

        // Простая проверка: если задержка разумная, считаем шаг успешным
        uint32_t step_time = (delay_us * 2) / 1000; // Примерное время в мс

        // Анализ времени выполнения шага
        if(step_time < 100) { // Если шаг выполнился быстро (менее 100мс)
            successful_steps++;
            total_step_time += step_time;
            if(step_time > max_time) {
                max_time = step_time;
            }
        }

        // Проверка на зависание
        if(step_time > 1000) { // Если шаг занял более 1 секунды
            break; // Прерываем тест
        }
    }

    // Остановка мотора
    Disable_Motor;
    DD16_Disble;

    // Расчет результатов
    if(successful_steps > 0) {
        result->success_rate = (successful_steps * 100) / CALIBRATION_TEST_STEPS;
        result->avg_step_time_us = total_step_time / successful_steps;
        result->max_step_time_us = max_time;

        // Оценка стабильности (чем меньше разброс времени, тем лучше)
        if(max_time > 0 && result->avg_step_time_us > 0) {
            uint16_t time_variation = max_time - result->avg_step_time_us;
            result->stability_score = 100 - (time_variation * 100) / result->avg_step_time_us;
            if(result->stability_score > 100) result->stability_score = 100;
        }
    }

    // Определение успешности теста
    if(result->success_rate >= CALIBRATION_SUCCESS_THRESHOLD) {
        return CALIBRATION_SUCCESS;
    } else {
        return CALIBRATION_ERROR_UNSTABLE;
    }
}

uint32_t Find_Optimal_Speed_Binary(uint8_t motor_id, uint32_t min_delay, uint32_t max_delay) {
    uint32_t optimal_delay = max_delay; // Начинаем с безопасного значения
    uint32_t left = min_delay;
    uint32_t right = max_delay;

    LCD_Send_Command(LCD_3_LINE_POS_0);
    LCD_SendString((uint8_t *)"Binary search...    ", 20);

    // Бинарный поиск оптимальной скорости
    while(right - left > CALIBRATION_STEP_US) {
        uint32_t test_delay = (left + right) / 2;

        LCD_Send_Command(LCD_4_LINE_POS_0);
        LCD_SendString((uint8_t *)"Testing speed...    ", 20);

        speed_test_result_t test_result;
        if(Test_Motor_Speed_Detailed(motor_id, test_delay, &test_result) == CALIBRATION_SUCCESS) {
            // Скорость работает, пробуем быстрее
            right = test_delay;
            optimal_delay = test_delay;
        } else {
            // Скорость не работает, пробуем медленнее
            left = test_delay;
        }

        Delay_mS(250);
        Delay_mS(250); // CALIBRATION_STABILITY_TIME (500мс)
    }

    return optimal_delay;
}

uint32_t Find_Max_Safe_Speed(uint8_t motor_id) {
    LCD_Send_Command(LCD_3_LINE_POS_0);
    LCD_SendString((uint8_t *)"Finding max speed...", 20);

    // Начинаем с консервативного значения и ускоряемся
    uint32_t current_delay = CALIBRATION_MAX_DELAY_US;
    uint32_t last_working_delay = current_delay;

    while(current_delay >= CALIBRATION_MIN_DELAY_US) {
        speed_test_result_t test_result;
        if(Test_Motor_Speed_Detailed(motor_id, current_delay, &test_result) == CALIBRATION_SUCCESS) {
            last_working_delay = current_delay;
            current_delay -= CALIBRATION_STEP_US; // Ускоряемся
        } else {
            break; // Достигли предела
        }

        Delay_mS(200); // Короткая пауза между тестами
    }

    return last_working_delay;
}

// =================================================================
// ВСПОМОГАТЕЛЬНЫЕ ФУНКЦИИ
// =================================================================

uint8_t Check_Motor_Ready_For_Calibration(uint8_t motor_id) {
    if(motor_id >= MAX_MOTORS) return 0;

    motor_config_t* motor = Get_Motor_Config(motor_id);
    if(!motor || !motor->enabled) return 0;

    // M7 - DC мотор, не калибруется
    if(motor_id == 7) return 0;

    return 1;
}

void Restore_Original_Settings(uint8_t motor_id) {
    if(motor_id >= MAX_MOTORS) return;

    // Восстанавливаем настройки по умолчанию из унифицированной системы
    // Эти значения уже установлены в Motor_System_Init()
    LCD_Send_Command(LCD_4_LINE_POS_0);
    LCD_SendString((uint8_t *)"Restoring defaults..", 20);
    Delay_mS(250);
    Delay_mS(250); // 500мс общая задержка
}

void Apply_Calibration_Results(uint8_t motor_id, motor_calibration_result_t* result) {
    if(motor_id >= MAX_MOTORS || result == NULL) return;

    // Применяем безопасную скорость (на 20% медленнее оптимальной)
    Set_Motor_Speed(motor_id, result->safe_delay_us);

    LCD_Send_Command(LCD_4_LINE_POS_0);
    LCD_SendString((uint8_t *)"Applied new settings", 20);
    Delay_mS(250);
    Delay_mS(250); // 500мс общая задержка
}

void Show_Calibration_Progress(uint8_t motor_id __attribute__((unused)), uint8_t progress_percent) {
    LCD_Send_Command(LCD_3_LINE_POS_0);
    if(progress_percent < 30) {
        LCD_SendString((uint8_t *)"Progress: Starting  ", 20);
    } else if(progress_percent < 60) {
        LCD_SendString((uint8_t *)"Progress: Testing   ", 20);
    } else if(progress_percent < 90) {
        LCD_SendString((uint8_t *)"Progress: Analyzing ", 20);
    } else {
        LCD_SendString((uint8_t *)"Progress: Finishing ", 20);
    }
}

void Show_Calibration_Results(motor_calibration_result_t* result) {
    if(result == NULL) return;

    LCD_Send_Command(LCD_1_LINE_POS_0);
    LCD_SendString((uint8_t *)"=== CALIB RESULT ===", 20);

    LCD_Send_Command(LCD_2_LINE_POS_0);
    LCD_SendString((uint8_t *)result->status_message, 20);

    if(result->calibration_status == CALIBRATION_SUCCESS) {
        LCD_Send_Command(LCD_3_LINE_POS_0);
        LCD_SendString((uint8_t *)"Status: SUCCESS     ", 20);

        LCD_Send_Command(LCD_4_LINE_POS_0);
        LCD_SendString((uint8_t *)"Settings applied    ", 20);
    } else {
        LCD_Send_Command(LCD_3_LINE_POS_0);
        LCD_SendString((uint8_t *)"Status: FAILED      ", 20);

        LCD_Send_Command(LCD_4_LINE_POS_0);
        LCD_SendString((uint8_t *)"Check motor/driver  ", 20);
    }

    // 3 секунды задержки
    for(uint8_t i = 0; i < 12; i++) {
        Delay_mS(250);
    }
}

uint8_t Validate_Calibration_Results(motor_calibration_result_t* result) {
    if(result == NULL) return 0;

    // Проверка разумности результатов
    if(result->optimal_delay_us < CALIBRATION_MIN_DELAY_US ||
       result->optimal_delay_us > CALIBRATION_MAX_DELAY_US) {
        return 0;
    }

    if(result->optimal_success_rate < CALIBRATION_SUCCESS_THRESHOLD) {
        return 0;
    }

    return 1;
}
