# Полноценные JSON команды (буфер 64 байта)

## ✅ Теперь поддерживаются полные JSON команды!

### Основные команды:

#### 1. Ко<PERSON><PERSON><PERSON>да READY
```json
{"cmd":"ready"}
```
**Размер**: 15 символов ✅

#### 2. Тест мотора M6
```json
{"cmd":"test_m6"}
```
**Размер**: 17 символов ✅

#### 3. Тест всех моторов
```json
{"cmd":"test_all"}
```
**Размер**: 18 символов ✅

#### 4. Остановить все моторы
```json
{"cmd":"stop_all"}
```
**Размер**: 18 символов ✅

#### 5. Загрузить конфигурацию
```json
{"cmd":"load_config"}
```
**Размер**: 21 символов ✅

#### 6. Сохранить конфигурацию
```json
{"cmd":"save_config"}
```
**Размер**: 21 символов ✅

### Расширенные команды:

#### 7. Движение мотора с параметрами
```json
{"cmd":"move_motor","motor":"M1","steps":1000}
```
**Размер**: 42 символа ✅

#### 8. Настройка скорости мотора
```json
{"cmd":"set_speed","motor":"M6","delay":200}
```
**Размер**: 40 символов ✅

#### 9. Получить статус системы
```json
{"cmd":"get_status"}
```
**Размер**: 18 символов ✅

#### 10. Команда FIRE
```json
{"cmd":"fire"}
```
**Размер**: 13 символов ✅

### Команды с направлением:

#### 11. Движение мотора по часовой стрелке
```json
{"cmd":"move_motor","motor":"M2","dir":"cw","steps":500}
```
**Размер**: 50 символов ✅

#### 12. Движение мотора против часовой стрелки
```json
{"cmd":"move_motor","motor":"M1","dir":"ccw","steps":300}
```
**Размер**: 51 символ ✅

### Команды настройки:

#### 13. Настройка параметров мотора M6
```json
{"cmd":"set_speed","motor":"M6","delay":200,"width":100}
```
**Размер**: 52 символа ✅

#### 14. Тест мотора с длительностью
```json
{"cmd":"test_motor","motor":"M3","duration":5000}
```
**Размер**: 43 символа ✅

## Изменения в системе:

### ✅ Увеличен UART буфер:
- **Было**: 7 байт
- **Стало**: 64 байта

### ✅ Обновлен UART обработчик:
- Поддержка бинарных команд: `$<cmd><p1><p2><p3><p4>;`
- Поддержка JSON команд: `{"cmd":"ready"}`
- Автоматическое определение типа команды

### ✅ Улучшенная обработка:
- Защита от переполнения буфера
- Корректное завершение по символам `;` и `}`

## Примеры использования:

### Последовательность запуска:
```json
{"cmd":"load_config"}
{"cmd":"ready"}
{"cmd":"test_m6"}
```

### Точное управление моторами:
```json
{"cmd":"move_motor","motor":"M1","dir":"cw","steps":90}
{"cmd":"move_motor","motor":"M2","dir":"ccw","steps":55}
```

### Настройка скоростей:
```json
{"cmd":"set_speed","motor":"M6","delay":150,"width":75}
{"cmd":"set_speed","motor":"M1","delay":100,"width":50}
```

### Тестирование:
```json
{"cmd":"test_motor","motor":"M6","duration":3000}
{"cmd":"get_status"}
```

## Совместимость:

Система поддерживает **оба формата одновременно**:

### Бинарные команды (старый формат):
```
$<07><00><00><00><00>;  // ready
$<98><00><00><00><00>;  // test M6
$<99><00><00><00><00>;  // test all
```

### JSON команды (новый формат):
```json
{"cmd":"ready"}
{"cmd":"test_m6"}
{"cmd":"test_all"}
```

## Отладка:

### Успешная обработка:
- **LCD**: "JSON: READY command"
- **Действие**: Выполняется команда

### Ошибки:
- **"JSON Parse Error"** - неправильный JSON формат
- **"JSON: Unknown cmd"** - неизвестная команда

## Ограничения:

- **Максимальный размер команды**: 63 символа
- **Формат**: Однострочный JSON
- **Завершение**: Обязательно символом `}`

## Будущие улучшения:

1. **Ответы в JSON формате**
2. **Статус команд**
3. **Логирование на SD карту**
4. **Конфигурация через JSON файлы**
5. **Поддержка массивов команд**

---

**Теперь система поддерживает полноценные JSON команды до 64 символов!** 🚀
