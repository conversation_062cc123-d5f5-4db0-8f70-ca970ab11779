# 🚀 CORDON-82 КРАТКОЕ РУКОВОДСТВО ПОЛЬЗОВАТЕЛЯ
## Быстрый старт и основные команды

**Версия:** 2.0  
**Дата:** 14.06.2025  

---

## ⚡ **БЫСТРЫЙ СТАРТ**

### **1. Включение системы**
1. Подать питание на систему
2. Дождаться инициализации (LCD покажет готовность)
3. Выполнить команду **7** или **85** (READY)

### **2. Ежедневная проверка**
```bash
Команда 76 → MOTOR REPORT (10-15 сек)
```
Проверяет состояние всех моторов и выдает отчет о готовности системы.

### **3. Основная работа**
```bash
Команда 7  → READY (60 сек) - стандартная готовность
Команда 85 → FAST READY (44 сек) - ускоренная готовность (+27% скорости)
```

---

## 🎮 **ОСНОВНЫЕ КОМАНДЫ**

### **Команды готовности**
| Команда | Название | Время | Описание |
|---------|----------|-------|----------|
| **7** | READY | 60 сек | Стандартное приведение в исходное положение |
| **85** | FAST READY | 44 сек | Ускоренное приведение (+27% быстрее) |

### **Управление отдельными моторами**
| Команда | Мотор | Направление | Функция |
|---------|-------|-------------|---------|
| **8** | M1 | CW | Горизонтальная наводка по часовой |
| **9** | M1 | CCW | Горизонтальная наводка против часовой |
| **10** | M2 | CW | Вертикальная наводка вверх |
| **11** | M2 | CCW | Вертикальная наводка вниз |
| **12** | M3 | Forward | Каретка вверх |
| **13** | M3 | Back | Каретка вниз |
| **14** | M4 | Forward | Продольное выдвижение |
| **15** | M4 | Back | Продольное втягивание |
| **16** | M5 | Forward | Механизм загрузки вперед |
| **17** | M5 | Back | Механизм загрузки назад |

### **Диагностика и обслуживание**
| Команда | Название | Время | Описание |
|---------|----------|-------|----------|
| **76** | MOTOR REPORT | 10-15 сек | Детальный отчет о всех моторах |
| **80** | CALIBRATE M2 | 2 мин | Автокалибровка мотора M2 |
| **81** | CALIBRATE CRITICAL | 5 мин | Автокалибровка критичных моторов |
| **82** | CALIBRATE ALL | 15 мин | Автокалибровка всех моторов |
| **99** | MAX SPEED TEST | 2 мин | Тест всех моторов на максимальной скорости |

---

## 📊 **ИНТЕРПРЕТАЦИЯ ОТЧЕТОВ**

### **Команда 76 - MOTOR REPORT**
```
=== MOTOR REPORT ===
Motor: M1
Health: EXCELLENT     ← Состояние мотора
Status: OK           ← Наличие ошибок

=== SUMMARY ===
Healthy motors: MANY  ← Количество здоровых моторов
Critical motors: NO   ← Наличие критичных проблем
System: READY        ← Готовность системы
```

### **Классификация состояния моторов**
- **EXCELLENT** (>80%) - Отличное состояние, нормальная работа
- **GOOD** (60-80%) - Хорошее состояние, плановый контроль
- **WARNING** (40-60%) - Требует внимания, рекомендуется калибровка
- **CRITICAL** (<40%) - Критическое состояние, срочное обслуживание

### **Статус системы**
- **READY** - Система готова к работе
- **MAINTENANCE** - Требуется обслуживание

---

## 🔧 **ЕЖЕНЕДЕЛЬНОЕ ОБСЛУЖИВАНИЕ**

### **Понедельник - Диагностика**
```bash
Команда 76 → Проверка состояния всех моторов
```

### **Среда - Калибровка критичных**
```bash
Команда 81 → Автокалибровка проблемных моторов (5 мин)
```

### **Пятница - Полная калибровка (при необходимости)**
```bash
Команда 82 → Автокалибровка всех моторов (15 мин)
```

### **Тест производительности**
```bash
Команда 99 → Тест максимальных скоростей (2 мин)
```

---

## ⚠️ **ВАЖНЫЕ ПРЕДУПРЕЖДЕНИЯ**

### **Безопасность**
- ❌ **НЕ прерывать** выполнение команд READY (7, 85)
- ❌ **НЕ находиться** в рабочей зоне во время работы
- ✅ **ВСЕГДА проверять** отчет команды 76 перед работой
- ✅ **НЕМЕДЛЕННО останавливать** систему при звуковых сигналах

### **Мотор M6 (Барабан)**
- ⚠️ **Особый режим безопасности** - автоматическая остановка при обнаружении препятствий
- ⚠️ **Ограничение движения** - максимум 50 шагов за операцию
- ⚠️ **Контроль датчика D3** - немедленная остановка при срабатывании

### **Критические ситуации**
| Ситуация | Действие |
|----------|----------|
| Звуковой сигнал | Немедленно остановить систему |
| Сообщение "ERROR" | Выполнить команду 76 для диагностики |
| Зависание мотора | Отключить питание, проверить механику |
| "CRITICAL" в отчете | Срочно выполнить калибровку (команды 80-82) |

---

## 🔍 **БЫСТРОЕ УСТРАНЕНИЕ ПРОБЛЕМ**

### **Мотор не работает**
1. Команда 76 → проверка состояния
2. Команда 80-82 → автокалибровка
3. Проверить питание и подключения

### **Медленная работа**
1. Команда 85 → использовать FAST READY
2. Команда 82 → полная калибровка
3. Команда 99 → тест производительности

### **Ошибки датчиков**
1. Очистить датчики от загрязнений
2. Команда 7 → сброс системы
3. Проверить подключение проводов

### **Проблемы с M6**
1. Система автоматически остановит мотор (новая функция безопасности)
2. Проверить датчик D3
3. Очистить путь движения

---

## 📞 **ТЕХНИЧЕСКАЯ ПОДДЕРЖКА**

### **Самодиагностика**
- **Команда 76** - основная диагностика
- **Команда 99** - тест производительности
- **Команды 80-82** - автоматическое исправление проблем

### **Документация**
- **Полная документация:** `Documentation/CORDON-82_COMPLETE_DOCUMENTATION.md`
- **Технические отчеты:** Папка `Documentation/`

### **Логи и отчеты**
- Все операции отображаются на LCD дисплее
- Команда 76 предоставляет детальную статистику
- Система автоматически сохраняет статистику операций

---

## 🎯 **РЕКОМЕНДУЕМЫЙ РАБОЧИЙ ЦИКЛ**

### **Начало смены**
1. **Команда 76** - проверка состояния
2. **Команда 85** - быстрая готовность
3. Визуальный осмотр системы

### **Рабочий процесс**
1. Использование команд 8-17 для управления
2. Контроль сообщений на LCD
3. При необходимости команда 7/85 для сброса

### **Конец смены**
1. **Команда 7** - возврат в безопасное положение
2. **Команда 76** - финальная проверка
3. Отключение питания

---

**Краткое руководство подготовлено:** Инженерной службой CORDON-82  
**Для получения полной информации см.:** `CORDON-82_COMPLETE_DOCUMENTATION.md`
