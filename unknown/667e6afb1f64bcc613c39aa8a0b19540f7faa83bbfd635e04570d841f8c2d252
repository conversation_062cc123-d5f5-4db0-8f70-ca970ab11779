# Конфигурация моторов для системы управления
# Версия: 1.0
# Дата: 2024

# Формат: MOTOR_NAME=step_delay_us,pulse_width_us,max_speed_steps_sec,enable_acceleration,direction_invert,enable_motor

# M1 - Мотор горизонтальной наводки
# step_delay_us: задержка между шагами в микросекундах (10-10000)
# pulse_width_us: длительность импульса в микросекундах (5-5000)  
# max_speed_steps_sec: максимальная скорость в шагах/сек (100-50000)
# enable_acceleration: включить разгон (0/1)
# direction_invert: инвертировать направление (0/1)
# enable_motor: включить мотор (0/1)
M1=100,50,5000,0,0,1

# M2 - Мотор вертикальной наводки
M2=100,50,5000,0,0,1

# M3 - Мотор каретки (с разгоном)
M3=100,50,5000,1,0,1

# M4 - Мотор продольного перемещения механизма загрузки
M4=100,50,5000,0,0,1

# M5 - Мотор вращения механизма загрузки
M5=100,50,5000,0,0,1

# M6 - Мотор вращения барабана (МАКСИМАЛЬНАЯ МОЩНОСТЬ!)
# Уменьшенные задержки для максимального крутящего момента
M6=200,100,2500,0,0,1

# M7 - DC мотор сжатия и разжатия механизма захвата
# Для DC мотора параметры шагов не используются
M7=1000,500,1000,0,0,1

# Общие параметры
CONFIG_VERSION=1
DESCRIPTION=Production motor configuration v1.0

# Примечания:
# - Для увеличения скорости уменьшайте step_delay_us
# - Для увеличения мощности уменьшайте step_delay_us и pulse_width_us
# - M6 настроен на максимальную мощность для преодоления нагрузки
# - Не устанавливайте step_delay_us меньше 10 мкс - моторы будут пищать
# - Максимальная безопасная частота: 10 кГц (step_delay_us = 50)
# - Для M6 можно использовать до 25 кГц (step_delay_us = 20)

# Команды для управления конфигурацией:
# UART команда 96: $<96><00><00><00><00>; - загрузить конфигурацию с SD
# UART команда 97: $<97><00><00><00><00>; - сохранить конфигурацию на SD
# UART команда 7:  $<07><00><00><00><00>; - выполнить сценарий ready
# UART команда 98: $<98><00><00><00><00>; - тест M6 максимальной мощности
# UART команда 99: $<99><00><00><00><00>; - тест всех моторов

# Кнопки:
# SW1+SW2: выполнить сценарий ready
# SW1: тест M1
# SW2: тест M2  
# SW3: тест M3
# SW4: тест M4
# SW5: тест M5
# SW6: тест M6
