#ifndef __STM32F10x_H
#define __STM32F10x_H

#include <stdint.h>

// Base addresses
#define PERIPH_BASE           0x40000000UL
#define APB1PERIPH_BASE       PERIPH_BASE
#define APB2PERIPH_BASE       (PERIPH_BASE + 0x10000UL)
#define AHBPERIPH_BASE        (PERIPH_BASE + 0x20000UL)

// GPIO
#define GPIOA_BASE            (APB2PERIPH_BASE + 0x0800UL)
#define GPIOB_BASE            (APB2PERIPH_BASE + 0x0C00UL)
#define GPIOC_BASE            (APB2PERIPH_BASE + 0x1000UL)
#define GPIOD_BASE            (APB2PERIPH_BASE + 0x1400UL)
#define GPIOE_BASE            (APB2PERIPH_BASE + 0x1800UL)

// Timers
#define TIM2_BASE             (APB1PERIPH_BASE + 0x0000UL)
#define TIM3_BASE             (APB1PERIPH_BASE + 0x0400UL)
#define TIM4_BASE             (APB1PERIPH_BASE + 0x0800UL)

// USART
#define USART1_BASE           (APB2PERIPH_BASE + 0x3800UL)
#define USART2_BASE           (APB1PERIPH_BASE + 0x4400UL)

// ADC
#define ADC1_BASE             (APB2PERIPH_BASE + 0x2400UL)

// GPIO Structure
typedef struct
{
  volatile uint32_t CRL;
  volatile uint32_t CRH;
  volatile uint32_t IDR;
  volatile uint32_t ODR;
  volatile uint32_t BSRR;
  volatile uint32_t BRR;
  volatile uint32_t LCKR;
} GPIO_TypeDef;

// Timer Structure
typedef struct
{
  volatile uint32_t CR1;
  volatile uint32_t CR2;
  volatile uint32_t SMCR;
  volatile uint32_t DIER;
  volatile uint32_t SR;
  volatile uint32_t EGR;
  volatile uint32_t CCMR1;
  volatile uint32_t CCMR2;
  volatile uint32_t CCER;
  volatile uint32_t CNT;
  volatile uint32_t PSC;
  volatile uint32_t ARR;
  volatile uint32_t RCR;
  volatile uint32_t CCR1;
  volatile uint32_t CCR2;
  volatile uint32_t CCR3;
  volatile uint32_t CCR4;
  volatile uint32_t BDTR;
  volatile uint32_t DCR;
  volatile uint32_t DMAR;
} TIM_TypeDef;

// USART Structure
typedef struct
{
  volatile uint32_t SR;
  volatile uint32_t DR;
  volatile uint32_t BRR;
  volatile uint32_t CR1;
  volatile uint32_t CR2;
  volatile uint32_t CR3;
  volatile uint32_t GTPR;
} USART_TypeDef;

// ADC Structure
typedef struct
{
  volatile uint32_t SR;
  volatile uint32_t CR1;
  volatile uint32_t CR2;
  volatile uint32_t SMPR1;
  volatile uint32_t SMPR2;
  volatile uint32_t JOFR1;
  volatile uint32_t JOFR2;
  volatile uint32_t JOFR3;
  volatile uint32_t JOFR4;
  volatile uint32_t HTR;
  volatile uint32_t LTR;
  volatile uint32_t SQR1;
  volatile uint32_t SQR2;
  volatile uint32_t SQR3;
  volatile uint32_t JSQR;
  volatile uint32_t JDR1;
  volatile uint32_t JDR2;
  volatile uint32_t JDR3;
  volatile uint32_t JDR4;
  volatile uint32_t DR;
} ADC_TypeDef;

// Peripheral declarations
#define GPIOA               ((GPIO_TypeDef *) GPIOA_BASE)
#define GPIOB               ((GPIO_TypeDef *) GPIOB_BASE)
#define GPIOC               ((GPIO_TypeDef *) GPIOC_BASE)
#define GPIOD               ((GPIO_TypeDef *) GPIOD_BASE)
#define GPIOE               ((GPIO_TypeDef *) GPIOE_BASE)

#define TIM2                ((TIM_TypeDef *) TIM2_BASE)
#define TIM3                ((TIM_TypeDef *) TIM3_BASE)
#define TIM4                ((TIM_TypeDef *) TIM4_BASE)

#define USART1              ((USART_TypeDef *) USART1_BASE)
#define USART2              ((USART_TypeDef *) USART2_BASE)

#define ADC1                ((ADC_TypeDef *) ADC1_BASE)

// GPIO Bit definitions
#define GPIO_ODR_ODR0                        0x00000001UL
#define GPIO_ODR_ODR1                        0x00000002UL
#define GPIO_ODR_ODR2                        0x00000004UL
#define GPIO_ODR_ODR3                        0x00000008UL
#define GPIO_ODR_ODR4                        0x00000010UL
#define GPIO_ODR_ODR5                        0x00000020UL
#define GPIO_ODR_ODR6                        0x00000040UL
#define GPIO_ODR_ODR7                        0x00000080UL
#define GPIO_ODR_ODR8                        0x00000100UL
#define GPIO_ODR_ODR9                        0x00000200UL
#define GPIO_ODR_ODR10                       0x00000400UL
#define GPIO_ODR_ODR11                       0x00000800UL
#define GPIO_ODR_ODR12                       0x00001000UL
#define GPIO_ODR_ODR13                       0x00002000UL
#define GPIO_ODR_ODR14                       0x00004000UL
#define GPIO_ODR_ODR15                       0x00008000UL

#define GPIO_IDR_IDR0                        0x00000001UL
#define GPIO_IDR_IDR1                        0x00000002UL
#define GPIO_IDR_IDR2                        0x00000004UL
#define GPIO_IDR_IDR3                        0x00000008UL
#define GPIO_IDR_IDR4                        0x00000010UL
#define GPIO_IDR_IDR5                        0x00000020UL
#define GPIO_IDR_IDR6                        0x00000040UL
#define GPIO_IDR_IDR7                        0x00000080UL
#define GPIO_IDR_IDR8                        0x00000100UL
#define GPIO_IDR_IDR9                        0x00000200UL
#define GPIO_IDR_IDR10                       0x00000400UL
#define GPIO_IDR_IDR11                       0x00000800UL
#define GPIO_IDR_IDR12                       0x00001000UL
#define GPIO_IDR_IDR13                       0x00002000UL
#define GPIO_IDR_IDR14                       0x00004000UL
#define GPIO_IDR_IDR15                       0x00008000UL

#define GPIO_BSRR_BS13                       0x00002000UL

// Timer bit definitions
#define TIM_CR1_CEN                          0x00000001UL

// USART bit definitions
#define USART_SR_RXNE                        0x00000020UL

// ADC bit definitions
#define ADC_CR2_ADON                         0x00000001UL
#define ADC_CR2_CONT                         0x00000002UL
#define ADC_CR2_CAL                          0x00000004UL
#define ADC_CR2_ALIGN                        0x00000800UL
#define ADC_CR2_EXTSEL                       0x000E0000UL
#define ADC_CR2_EXTTRIG                      0x00100000UL
#define ADC_CR1_SCAN                         0x00000100UL
#define ADC_SQR1_L                           0x00F00000UL
#define ADC_SMPR2_SMP0_0                     0x00000001UL
#define ADC_SMPR2_SMP0_1                     0x00000002UL
#define ADC_CR2_SWSTART                      0x00400000UL
#define ADC_SR_EOC                           0x00000002UL
#define USART_SR_TC                          0x00000040UL

// RCC definitions
#define RCC_BASE              (AHBPERIPH_BASE + 0x1000UL)
typedef struct
{
  volatile uint32_t CR;
  volatile uint32_t CFGR;
  volatile uint32_t CIR;
  volatile uint32_t APB2RSTR;
  volatile uint32_t APB1RSTR;
  volatile uint32_t AHBENR;
  volatile uint32_t APB2ENR;
  volatile uint32_t APB1ENR;
  volatile uint32_t BDCR;
  volatile uint32_t CSR;
} RCC_TypeDef;

#define RCC                 ((RCC_TypeDef *) RCC_BASE)

// RCC bit definitions
#define RCC_APB2ENR_IOPAEN                   0x00000004UL
#define RCC_APB2ENR_IOPBEN                   0x00000008UL
#define RCC_APB2ENR_IOPCEN                   0x00000010UL
#define RCC_APB2ENR_IOPDEN                   0x00000020UL
#define RCC_APB2ENR_IOPEEN                   0x00000040UL
#define RCC_APB2ENR_AFIOEN                   0x00000001UL
#define RCC_APB2ENR_ADC1EN                   0x00000200UL
#define RCC_APB2ENR_USART1EN                 0x00004000UL
#define RCC_APB2ENR_TIM1EN                   0x00000800UL
#define RCC_APB1ENR_TIM2EN                   0x00000001UL
#define RCC_APB1ENR_TIM3EN                   0x00000002UL
#define RCC_APB1ENR_TIM4EN                   0x00000004UL
#define RCC_APB1ENR_USART2EN                 0x00020000UL
#define RCC_APB1ENR_I2C1EN                   0x00200000UL

// AFIO definitions
#define AFIO_BASE             (APB2PERIPH_BASE + 0x0000UL)
typedef struct
{
  volatile uint32_t EVCR;
  volatile uint32_t MAPR;
  volatile uint32_t EXTICR[4];
  uint32_t RESERVED0;
  volatile uint32_t MAPR2;
} AFIO_TypeDef;

#define AFIO                ((AFIO_TypeDef *) AFIO_BASE)

// I2C definitions
#define I2C1_BASE             (APB1PERIPH_BASE + 0x5400UL)
typedef struct
{
  volatile uint32_t CR1;
  volatile uint32_t CR2;
  volatile uint32_t OAR1;
  volatile uint32_t OAR2;
  volatile uint32_t DR;
  volatile uint32_t SR1;
  volatile uint32_t SR2;
  volatile uint32_t CCR;
  volatile uint32_t TRISE;
} I2C_TypeDef;

#define I2C1                ((I2C_TypeDef *) I2C1_BASE)

// I2C bit definitions
#define I2C_CR1_PE                           0x00000001UL
#define I2C_CR1_START                        0x00000100UL
#define I2C_CR1_STOP                         0x00000200UL
#define I2C_CR1_ACK                          0x00000400UL
#define I2C_CR2_FREQ                         0x0000003FUL
#define I2C_CCR_CCR                          0x00000FFFUL
#define I2C_SR1_SB                           0x00000001UL
#define I2C_SR1_ADDR                         0x00000002UL
#define I2C_SR1_BTF                          0x00000004UL
#define I2C_SR1_TXE                          0x00000080UL
#define I2C_SR1_RXNE                         0x00000040UL
#define I2C_SR2_MSL                          0x00000001UL
#define I2C_SR2_BUSY                         0x00000002UL
#define I2C_SR2_TRA                          0x00000004UL

// PWR definitions
#define PWR_BASE              (APB1PERIPH_BASE + 0x7000UL)
typedef struct
{
  volatile uint32_t CR;
  volatile uint32_t CSR;
} PWR_TypeDef;

#define PWR                 ((PWR_TypeDef *) PWR_BASE)
#define PWR_CSR_EWUP                         0x00000100UL

// AFIO bit definitions
#define AFIO_MAPR_SWJ_CFG_JTAGDISABLE        0x02000000UL

// GPIO mode definitions
#define GPIO_CRL_MODE0                       0x00000003UL
#define GPIO_CRL_CNF0                        0x0000000CUL
#define GPIO_CRL_MODE1                       0x00000030UL
#define GPIO_CRL_CNF1                        0x000000C0UL
#define GPIO_CRL_MODE2                       0x00000300UL
#define GPIO_CRL_CNF2                        0x00000C00UL
#define GPIO_CRL_MODE3                       0x00003000UL
#define GPIO_CRL_CNF3                        0x0000C000UL
#define GPIO_CRL_MODE4                       0x00030000UL
#define GPIO_CRL_CNF4                        0x000C0000UL
#define GPIO_CRL_MODE5                       0x00300000UL
#define GPIO_CRL_CNF5                        0x00C00000UL
#define GPIO_CRL_MODE6                       0x03000000UL
#define GPIO_CRL_CNF6                        0x0C000000UL
#define GPIO_CRL_MODE7                       0x30000000UL
#define GPIO_CRL_CNF7                        0xC0000000UL

#define GPIO_CRH_MODE8                       0x00000003UL
#define GPIO_CRH_CNF8                        0x0000000CUL
#define GPIO_CRH_MODE9                       0x00000030UL
#define GPIO_CRH_CNF9                        0x000000C0UL
#define GPIO_CRH_MODE10                      0x00000300UL
#define GPIO_CRH_CNF10                       0x00000C00UL
#define GPIO_CRH_MODE11                      0x00003000UL
#define GPIO_CRH_CNF11                       0x0000C000UL
#define GPIO_CRH_MODE12                      0x00030000UL
#define GPIO_CRH_CNF12                       0x000C0000UL
#define GPIO_CRH_MODE13                      0x00300000UL
#define GPIO_CRH_CNF13                       0x00C00000UL
#define GPIO_CRH_MODE14                      0x03000000UL
#define GPIO_CRH_CNF14                       0x0C000000UL
#define GPIO_CRH_MODE15                      0x30000000UL
#define GPIO_CRH_CNF15                       0xC0000000UL

// GPIO CNF individual bits
#define GPIO_CRL_CNF0_0                      0x00000004UL
#define GPIO_CRL_CNF1_0                      0x00000040UL
#define GPIO_CRL_CNF2_0                      0x00000400UL
#define GPIO_CRL_CNF3_0                      0x00004000UL
#define GPIO_CRL_CNF4_0                      0x00040000UL
#define GPIO_CRL_CNF5_0                      0x00400000UL
#define GPIO_CRL_CNF6_0                      0x04000000UL
#define GPIO_CRL_CNF7_0                      0x40000000UL

#define GPIO_CRH_CNF8_0                      0x00000004UL
#define GPIO_CRH_CNF9_0                      0x00000040UL
#define GPIO_CRH_CNF10_0                     0x00000400UL
#define GPIO_CRH_CNF11_0                     0x00004000UL
#define GPIO_CRH_CNF12_0                     0x00040000UL
#define GPIO_CRH_CNF13_0                     0x00400000UL
#define GPIO_CRH_CNF14_0                     0x04000000UL
#define GPIO_CRH_CNF15_0                     0x40000000UL

// GPIO MODE individual bits
#define GPIO_CRL_MODE0_0                     0x00000001UL
#define GPIO_CRL_MODE0_1                     0x00000002UL
#define GPIO_CRL_MODE1_0                     0x00000010UL
#define GPIO_CRL_MODE1_1                     0x00000020UL
#define GPIO_CRL_MODE2_0                     0x00000100UL
#define GPIO_CRL_MODE2_1                     0x00000200UL
#define GPIO_CRL_MODE3_0                     0x00001000UL
#define GPIO_CRL_MODE3_1                     0x00002000UL
#define GPIO_CRL_MODE4_0                     0x00010000UL
#define GPIO_CRL_MODE4_1                     0x00020000UL
#define GPIO_CRL_MODE5_0                     0x00100000UL
#define GPIO_CRL_MODE5_1                     0x00200000UL
#define GPIO_CRL_MODE6_0                     0x01000000UL
#define GPIO_CRL_MODE6_1                     0x02000000UL
#define GPIO_CRL_MODE7_0                     0x10000000UL
#define GPIO_CRL_MODE7_1                     0x20000000UL

#define GPIO_CRH_MODE8_0                     0x00000001UL
#define GPIO_CRH_MODE8_1                     0x00000002UL
#define GPIO_CRH_MODE9_0                     0x00000010UL
#define GPIO_CRH_MODE9_1                     0x00000020UL
#define GPIO_CRH_MODE10_0                    0x00000100UL
#define GPIO_CRH_MODE10_1                    0x00000200UL
#define GPIO_CRH_MODE11_0                    0x00001000UL
#define GPIO_CRH_MODE11_1                    0x00002000UL
#define GPIO_CRH_MODE12_0                    0x00010000UL
#define GPIO_CRH_MODE12_1                    0x00020000UL
#define GPIO_CRH_MODE13_0                    0x00100000UL
#define GPIO_CRH_MODE13_1                    0x00200000UL
#define GPIO_CRH_MODE14_0                    0x01000000UL
#define GPIO_CRH_MODE14_1                    0x02000000UL
#define GPIO_CRH_MODE15_0                    0x10000000UL
#define GPIO_CRH_MODE15_1                    0x20000000UL

// GPIO CNF individual bits _1
#define GPIO_CRL_CNF0_1                      0x00000008UL
#define GPIO_CRL_CNF1_1                      0x00000080UL
#define GPIO_CRL_CNF2_1                      0x00000800UL
#define GPIO_CRL_CNF3_1                      0x00008000UL
#define GPIO_CRL_CNF4_1                      0x00080000UL
#define GPIO_CRL_CNF5_1                      0x00800000UL
#define GPIO_CRL_CNF6_1                      0x08000000UL
#define GPIO_CRL_CNF7_1                      0x80000000UL

#define GPIO_CRH_CNF8_1                      0x00000008UL
#define GPIO_CRH_CNF9_1                      0x00000080UL
#define GPIO_CRH_CNF10_1                     0x00000800UL
#define GPIO_CRH_CNF11_1                     0x00008000UL
#define GPIO_CRH_CNF12_1                     0x00080000UL
#define GPIO_CRH_CNF13_1                     0x00800000UL
#define GPIO_CRH_CNF14_1                     0x08000000UL
#define GPIO_CRH_CNF15_1                     0x80000000UL

// USART bit definitions
#define USART_CR1_UE                         0x00002000UL
#define USART_CR1_M                          0x00001000UL
#define USART_CR1_WAKE                       0x00000800UL
#define USART_CR1_PCE                        0x00000400UL
#define USART_CR1_PS                         0x00000200UL
#define USART_CR1_PEIE                       0x00000100UL
#define USART_CR1_TXEIE                      0x00000080UL
#define USART_CR1_TCIE                       0x00000040UL
#define USART_CR1_RXNEIE                     0x00000020UL
#define USART_CR1_IDLEIE                     0x00000010UL
#define USART_CR1_TE                         0x00000008UL
#define USART_CR1_RE                         0x00000004UL
#define USART_CR1_RWU                        0x00000002UL
#define USART_CR1_SBK                        0x00000001UL

#define USART_SR_CTS                         0x00000200UL
#define USART_SR_LBD                         0x00000100UL
#define USART_SR_TXE                         0x00000080UL
#define USART_SR_IDLE                        0x00000010UL
#define USART_SR_ORE                         0x00000008UL
#define USART_SR_NE                          0x00000004UL
#define USART_SR_FE                          0x00000002UL
#define USART_SR_PE                          0x00000001UL

// Timer additional bit definitions
#define TIM_SR_UIF                           0x00000001UL
#define TIM_DIER_UIE                         0x00000001UL
#define TIM_EGR_UG                           0x00000001UL

// NVIC definitions
#define NVIC_BASE             (0xE000E100UL)
typedef struct
{
  volatile uint32_t ISER[8U];
  uint32_t RESERVED0[24U];
  volatile uint32_t ICER[8U];
  uint32_t RESERVED1[24U];
  volatile uint32_t ISPR[8U];
  uint32_t RESERVED2[24U];
  volatile uint32_t ICPR[8U];
  uint32_t RESERVED3[24U];
  volatile uint32_t IABR[8U];
  uint32_t RESERVED4[56U];
  volatile uint8_t  IP[240U];
  uint32_t RESERVED5[644U];
  volatile uint32_t STIR;
} NVIC_Type;

#define NVIC                ((NVIC_Type *) NVIC_BASE)

// SysTick definitions
#define SysTick_BASE          (0xE000E010UL)
typedef struct
{
  volatile uint32_t CTRL;
  volatile uint32_t LOAD;
  volatile uint32_t VAL;
  volatile uint32_t CALIB;
} SysTick_Type;

#define SysTick             ((SysTick_Type *) SysTick_BASE)

// SCB definitions
#define SCB_BASE              (0xE000ED00UL)
typedef struct
{
  volatile uint32_t CPUID;
  volatile uint32_t ICSR;
  volatile uint32_t VTOR;
  volatile uint32_t AIRCR;
  volatile uint32_t SCR;
  volatile uint32_t CCR;
  volatile uint8_t  SHP[12U];
  volatile uint32_t SHCSR;
  volatile uint32_t CFSR;
  volatile uint32_t HFSR;
  volatile uint32_t DFSR;
  volatile uint32_t MMFAR;
  volatile uint32_t BFAR;
  volatile uint32_t AFSR;
  volatile uint32_t PFR[2U];
  volatile uint32_t DFR;
  volatile uint32_t ADR;
  volatile uint32_t MMFR[4U];
  volatile uint32_t ISAR[5U];
  uint32_t RESERVED0[5U];
  volatile uint32_t CPACR;
} SCB_Type;

#define SCB                 ((SCB_Type *) SCB_BASE)

// IRQ Numbers
typedef enum IRQn
{
  TIM2_IRQn                   = 28,
  TIM4_IRQn                   = 30,
  USART1_IRQn                 = 37,
  USART2_IRQn                 = 38,
} IRQn_Type;

// NVIC functions (dummy implementations)
static inline void NVIC_EnableIRQ(IRQn_Type IRQn) { (void)IRQn; }
static inline void NVIC_SetPriority(IRQn_Type IRQn, uint32_t priority) { (void)IRQn; (void)priority; }
static inline void __enable_irq(void) { }

#endif /* __STM32F10x_H */
