# =================================================================
# КОНФИГУРАЦИЯ МОТОРОВ - motor_config.txt
# =================================================================
# Этот файл должен находиться в корне SD карты
# Система автоматически загружает параметры при старте
# 
# UART команды:
# $<96><00><00><00><00>; - загрузить этот файл с SD карты
# $<97><00><00><00><00>; - сохранить текущие параметры на SD карту
# =================================================================

# Формат строки: MOTOR=step_delay,pulse_width,max_speed,accel,invert,enable
# step_delay: задержка между шагами в микросекундах (10-10000)
# pulse_width: длительность импульса в микросекундах (5-5000)
# max_speed: максимальная скорость в шагах/сек (100-50000)
# accel: включить разгон 1=да, 0=нет
# invert: инвертировать направление 1=да, 0=нет  
# enable: включить мотор 1=да, 0=нет

# -----------------------------------------------------------------
# M1 - МОТОР ГОРИЗОНТАЛЬНОЙ НАВОДКИ (NEMA23 + редуктор 1:40)
# -----------------------------------------------------------------
# СТАРЫЕ РАБОЧИЕ настройки: Delay_mS(5)+Delay_mS(5) = 100 Гц (ПРОВЕРЕНО!)
# Крутящий момент: 3 Н·м × 40 = 120 Н·м на выходе
# ИСПРАВЛЕНО: используем старые настройки из Cordon\Include
M1=5000,5000,100,0,0,1

# -----------------------------------------------------------------  
# M2 - МОТОР ВЕРТИКАЛЬНОЙ НАВОДКИ
# -----------------------------------------------------------------
# СТАРЫЕ РАБОЧИЕ настройки: Delay_mS(200)+Delay_mS(50) = 4 Гц (ПРОВЕРЕНО!)
M2=200000,50000,4,0,0,1

# -----------------------------------------------------------------
# M3 - МОТОР КАРЕТКИ
# -----------------------------------------------------------------
# СТАРЫЕ РАБОЧИЕ настройки: Delay_mS(5)+Delay_mS(5) = 100 Гц (ПРОВЕРЕНО!)
M3=5000,5000,100,1,0,1

# -----------------------------------------------------------------
# M4 - МОТОР ПРОДОЛЬНОГО ПЕРЕМЕЩЕНИЯ МЕХАНИЗМА ЗАГРУЗКИ
# -----------------------------------------------------------------
# СТАРЫЕ РАБОЧИЕ настройки: Delay_mS(5)+Delay_mS(5) = 100 Гц (ПРОВЕРЕНО!)
M4=5000,5000,100,0,0,1

# -----------------------------------------------------------------
# M5 - МОТОР ВРАЩЕНИЯ МЕХАНИЗМА ЗАГРУЗКИ
# -----------------------------------------------------------------
# СТАРЫЕ РАБОЧИЕ настройки: Delay_mS(5)+Delay_mS(5) = 100 Гц (ПРОВЕРЕНО!)
M5=5000,5000,100,0,0,1

# -----------------------------------------------------------------
# M6 - МОТОР ВРАЩЕНИЯ БАРАБАНА *** МАКСИМАЛЬНАЯ МОЩНОСТЬ ***
# -----------------------------------------------------------------
# СТАРЫЕ РАБОЧИЕ настройки: Delay_mS(4)+Delay_mS(1) = 200 Гц (ПРОВЕРЕНО!)
# Максимальная мощность для вращения барабана
# step_delay=4000, pulse_width=1000 = 200 Гц = МАКСИМАЛЬНЫЙ МОМЕНТ
M6=4000,1000,200,0,0,1

# -----------------------------------------------------------------
# M7 - DC МОТОР СЖАТИЯ И РАЗЖАТИЯ МЕХАНИЗМА ЗАХВАТА
# -----------------------------------------------------------------
# Для DC мотора параметры шагов не используются, но должны быть указаны
M7=1000,350,2000,0,0,1

# =================================================================
# ОБЩИЕ ПАРАМЕТРЫ
# =================================================================
CONFIG_VERSION=1
DESCRIPTION=Production motor config v1.0 - optimized for field use

# =================================================================
# СПРАВОЧНАЯ ИНФОРМАЦИЯ
# =================================================================

# ЧАСТОТЫ И СКОРОСТИ:
# step_delay=10   -> 50 кГц -> 50000 шагов/сек (МАКСИМУМ, может пищать)
# step_delay=20   -> 25 кГц -> 25000 шагов/сек (высокая скорость)  
# step_delay=50   -> 10 кГц -> 10000 шагов/сек (оптимальная скорость)
# step_delay=100  ->  5 кГц ->  5000 шагов/сек (текущая настройка)
# step_delay=200  ->  2.5кГц->  2500 шагов/сек (для M6 - макс. мощность)

# РЕКОМЕНДАЦИИ ПО НАСТРОЙКЕ:
# 1. Для увеличения СКОРОСТИ - уменьшайте step_delay
# 2. Для увеличения МОЩНОСТИ - уменьшайте step_delay И pulse_width  
# 3. Если мотор ПИЩИТ - увеличьте step_delay до 50-100 мкс
# 4. Если мотор НЕ ТЯНЕТ - уменьшите step_delay (для M6 до 50 мкс)
# 5. M6 критически важен - он должен крутить барабан под нагрузкой

# БЕЗОПАСНЫЕ ПРЕДЕЛЫ:
# step_delay: 10-10000 мкс (не меньше 10!)
# pulse_width: 5-5000 мкс  
# max_speed: 100-50000 шагов/сек

# КОМАНДЫ UART ДЛЯ ТЕСТИРОВАНИЯ:
# $<00><00><00><00><00>; - RESET: возврат всех моторов в исходные позиции
# $<07><00><00><00><00>; - выполнить сценарий READY (SW1+SW2)
# $<93><00><00><00><00>; - показать страницу "О программе" с мелодией
# $<94><00><00><00><00>; - воспроизвести мелодию "Happy Birthday"
# $<95><00><00><00><00>; - воспроизвести стартовую мелодию (20 beep)
# $<96><00><00><00><00>; - загрузить встроенную конфигурацию
# $<97><00><00><00><00>; - показать текущую конфигурацию
# $<98><00><00><00><00>; - тест M6 максимальной мощности
# $<99><00><00><00><00>; - тест всех моторов

# КНОПКИ:
# SW1+SW2 = сценарий READY
# SW5+SW6 = страница "О программе" (контакты, копирайт, зацикленная мелодия)
# SW1 = тест M1, SW2 = тест M2, SW3 = тест M3
# SW4 = тест M4, SW5 = тест M5, SW6 = тест M6 (или выход из About)

# =================================================================
# ИСТОРИЯ ИЗМЕНЕНИЙ
# =================================================================
# v1.0 - Начальная конфигурация, M1-M5 на 5кГц, M6 на максимальной мощности
# v2.0 - ОПТИМИЗИРОВАННАЯ конфигурация:
#        M1: 40кГц (8x быстрее) с редуктором 1:40 = 1000 об/мин
#        M2-M5: 20кГц (4x быстрее)
#        M6: 10кГц (7x быстрее) максимальная мощность
# v2.1 - Добавлена страница "О программе" и мелодия "Happy Birthday":
#        SW5+SW6 = About page с контактами и зацикленной мелодией
#        Команда 93 = About page, Команда 94 = Happy Birthday melody
#
# JSON КОМАНДЫ:
# {"cmd":"reset"} - возврат всех моторов в исходные позиции
# {"cmd":"ready"} - выполнить сценарий READY
# {"cmd":"about"} - показать страницу "О программе"
# {"cmd":"play_melody"} - воспроизвести мелодию "Happy Birthday"
# {"cmd":"test_all"} - тест всех моторов
# {"cmd":"load_config"} - загрузить встроенную конфигурацию
# {"cmd":"show_config"} - показать текущую конфигурацию
#
# v2.2 - УБРАНА SD КАРТА - конфигурация встроена в прошивку:
#        Команда 96 = загрузить встроенную конфигурацию
#        Команда 97 = показать текущую конфигурацию
#        JSON: load_config, show_config
# v2.3 - ИСПРАВЛЕН ПИСК МОТОРОВ - снижены частоты до рабочих:
#        M1: 2 кГц (вместо 40 кГц) - НЕ ПИЩИТ!
#        M2-M5: 1 кГц (вместо 20 кГц) - НЕ ПИЩАТ!
#        M6: 500 Гц (вместо 10 кГц) - НЕ ПИЩИТ, МАКСИМАЛЬНЫЙ МОМЕНТ!
# v2.4 - НАСТРОЙКИ ВЫНЕСЕНЫ В main.c - МОЖНО МЕНЯТЬ ВРУЧНУЮ:
#        Все настройки моторов теперь в начале main.c (строки 18-73)
#        Функции вращения используют переменные из main.c
#        Можно легко менять частоты без перекомпиляции
# v2.5 - SW6 НАЗНАЧЕНА НА READY COMMAND:
#        SW6 теперь выполняет полный алгоритм READY
#        Звуковые сигналы: успех (3 коротких), ошибка (1 длинный)
#        Проверка датчиков и отображение результата на LCD
#
# ВСТРОЕННАЯ КОНФИГУРАЦИЯ:
# Все параметры моторов теперь хранятся в прошивке (UserFunction.c)
# Для изменения параметров нужно перекомпилировать прошивку
# Преимущества: надежность, быстрота, нет зависимости от SD карты
# =================================================================
