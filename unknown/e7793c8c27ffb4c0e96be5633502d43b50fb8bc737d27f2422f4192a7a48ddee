# Примеры JSO<PERSON> команд для системы управления моторами

## Основные команды

### 1. Команда READY (подготовка к работе)
```json
{"cmd":"ready"}
```
Эквивалент: $<07><00><00><00><00>; или SW1+SW2

### 2. Тест мотора M6 (максимальная мощность)
```json
{"cmd":"test_m6"}
```
Эквивалент: $<98><00><00><00><00>;

### 3. Тест всех моторов
```json
{"cmd":"test_all"}
```
Эквивалент: $<99><00><00><00><00>;

### 4. Остановить все моторы
```json
{"cmd":"stop_all"}
```

### 5. Загрузить конфигурацию с SD карты
```json
{"cmd":"load_config"}
```
Эквивалент: $<96><00><00><00><00>;

### 6. Сохранить конфигурацию на SD карту
```json
{"cmd":"save_config"}
```
Эквивалент: $<97><00><00><00><00>;

## Расширенные команды (для будущей реализации)

### 7. Движение конкретного мотора
```json
{"cmd":"move_motor","motor":"M1","steps":1000,"speed":5000}
```

### 8. Движение мотора в определенном направлении
```json
{"cmd":"move_motor","motor":"M2","direction":"cw","steps":500}
```

### 9. Настройка скорости мотора
```json
{"cmd":"set_speed","motor":"M6","step_delay":200,"pulse_width":100}
```

### 10. Получить статус системы
```json
{"cmd":"get_status"}
```

## Как отправлять JSON команды

### Через UART:
1. **Простые команды** (помещаются в 7 байт):
   ```
   {"cmd":"ready"}  // 15 символов - НЕ ПОМЕСТИТСЯ
   ```

2. **Сокращенные команды** (помещаются в 7 байт):
   ```
   {"c":"r"}        // ready - 9 символов ✓
   {"c":"t6"}       // test_m6 - 10 символов ✓  
   {"c":"ta"}       // test_all - 10 символов ✓
   {"c":"s"}        // stop_all - 9 символов ✓
   ```

### Рекомендуемый формат для UART (7 байт):
```
{"c":"r"}   - ready command
{"c":"t6"}  - test M6
{"c":"ta"}  - test all motors  
{"c":"s"}   - stop all
{"c":"lc"}  - load config
{"c":"sc"}  - save config
```

## Текущая реализация

### ✅ Работает:
- Парсинг JSON команд
- Обработка команд: ready, test_m6, test_all, stop_all
- Отображение на LCD
- Совместимость с бинарными командами

### 🔄 В разработке:
- Команды load_config, save_config (заглушки)
- Команды move_motor, set_speed
- Команда get_status

### ⚠️ Ограничения:
- UART буфер только 7 байт
- JSON команды должны быть короткими
- Нет поддержки многострочных JSON

## Примеры использования

### 1. Последовательность запуска:
```json
{"c":"lc"}   // Загрузить конфигурацию
{"c":"r"}    // Выполнить ready
{"c":"t6"}   // Тест M6
```

### 2. Аварийная остановка:
```json
{"c":"s"}    // Остановить все моторы
```

### 3. Тестирование:
```json
{"c":"ta"}   // Тест всех моторов
```

## Отладка

### Проверка парсинга:
1. Отправьте: `{"c":"r"}`
2. На LCD должно появиться: "JSON: READY command"
3. Система выполнит команду ready

### Ошибки парсинга:
- "JSON Parse Error" - неправильный формат JSON
- "JSON: Unknown cmd" - неизвестная команда

## Будущие улучшения

1. **Расширение буфера UART** до 64-128 байт
2. **Поддержка параметров** в JSON командах
3. **Ответы в JSON формате**
4. **Логирование команд** на SD карту
5. **Конфигурация через JSON** файлы

## Совместимость

Система поддерживает **оба формата одновременно**:
- **Бинарные команды**: `$<07><00><00><00><00>;`
- **JSON команды**: `{"c":"r"}`

Выбор формата определяется автоматически по первому символу:
- `{` = JSON команда
- `$` = бинарная команда
