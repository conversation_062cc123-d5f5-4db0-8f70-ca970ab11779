#include "motor_config.h"
#include "main.h"
#include "lcd.h"

// Глобальная переменная конфигурации
AllMotorsConfig_t g_MotorConfig;

// Простые реализации функций вместо string.h
static void simple_memset(void* ptr, int value, uint32_t size)
{
    uint8_t* p = (uint8_t*)ptr;
    for(uint32_t i = 0; i < size; i++) {
        p[i] = (uint8_t)value;
    }
}

static void simple_memcpy(void* dest, const void* src, uint32_t size)
{
    uint8_t* d = (uint8_t*)dest;
    const uint8_t* s = (const uint8_t*)src;
    for(uint32_t i = 0; i < size; i++) {
        d[i] = s[i];
    }
}

static void simple_strcpy(char* dest, const char* src)
{
    while(*src) {
        *dest++ = *src++;
    }
    *dest = '\0';
}

// Простая реализация SD карты (заглушка - нужно будет добавить реальную работу с SD)
// Пока сохраняем во внутренней Flash памяти

// Адрес во Flash для хранения конфигурации (последние страницы Flash)
#define CONFIG_FLASH_ADDRESS    0x0807F000  // Последние 4KB Flash

// Функция загрузки значений по умолчанию
uint8_t MotorConfig_LoadDefaults(AllMotorsConfig_t* config)
{
    if (!config) return 0;
    
    // Очищаем структуру
    simple_memset(config, 0, sizeof(AllMotorsConfig_t));
    
    // M1 - Мотор горизонтальной наводки
    config->M1.step_delay_us = DEFAULT_M1_STEP_DELAY;
    config->M1.pulse_width_us = DEFAULT_M1_PULSE_WIDTH;
    config->M1.max_speed_steps_sec = DEFAULT_M1_MAX_SPEED;
    config->M1.enable_motor = 1;
    config->M1.enable_acceleration = 0;
    config->M1.direction_invert = 0;
    
    // M2 - Мотор вертикальной наводки
    config->M2.step_delay_us = DEFAULT_M2_STEP_DELAY;
    config->M2.pulse_width_us = DEFAULT_M2_PULSE_WIDTH;
    config->M2.max_speed_steps_sec = DEFAULT_M2_MAX_SPEED;
    config->M2.enable_motor = 1;
    config->M2.enable_acceleration = 0;
    config->M2.direction_invert = 0;
    
    // M3 - Мотор каретки
    config->M3.step_delay_us = DEFAULT_M3_STEP_DELAY;
    config->M3.pulse_width_us = DEFAULT_M3_PULSE_WIDTH;
    config->M3.max_speed_steps_sec = DEFAULT_M3_MAX_SPEED;
    config->M3.enable_motor = 1;
    config->M3.enable_acceleration = 1;
    config->M3.acceleration_steps = 1000;
    config->M3.direction_invert = 0;
    
    // M4 - Мотор продольного перемещения механизма загрузки
    config->M4.step_delay_us = DEFAULT_M4_STEP_DELAY;
    config->M4.pulse_width_us = DEFAULT_M4_PULSE_WIDTH;
    config->M4.max_speed_steps_sec = DEFAULT_M4_MAX_SPEED;
    config->M4.enable_motor = 1;
    config->M4.enable_acceleration = 0;
    config->M4.direction_invert = 0;
    
    // M5 - Мотор вращения механизма загрузки
    config->M5.step_delay_us = DEFAULT_M5_STEP_DELAY;
    config->M5.pulse_width_us = DEFAULT_M5_PULSE_WIDTH;
    config->M5.max_speed_steps_sec = DEFAULT_M5_MAX_SPEED;
    config->M5.enable_motor = 1;
    config->M5.enable_acceleration = 0;
    config->M5.direction_invert = 0;
    
    // M6 - Мотор вращения барабана (МАКСИМАЛЬНАЯ МОЩНОСТЬ)
    config->M6.step_delay_us = DEFAULT_M6_STEP_DELAY;
    config->M6.pulse_width_us = DEFAULT_M6_PULSE_WIDTH;
    config->M6.max_speed_steps_sec = DEFAULT_M6_MAX_SPEED;
    config->M6.enable_motor = 1;
    config->M6.enable_acceleration = 0;
    config->M6.direction_invert = 0;
    
    // M7 - DC мотор сжатия и разжатия механизма захвата
    config->M7.step_delay_us = DEFAULT_M7_STEP_DELAY;
    config->M7.pulse_width_us = DEFAULT_M7_PULSE_WIDTH;
    config->M7.max_speed_steps_sec = DEFAULT_M7_MAX_SPEED;
    config->M7.enable_motor = 1;
    config->M7.enable_acceleration = 0;
    config->M7.direction_invert = 0;
    
    // Общие параметры
    config->config_version = CONFIG_VERSION;
    config->last_update = 0;
    simple_strcpy(config->description, "Default motor configuration");
    
    // Вычисляем контрольную сумму
    config->checksum = MotorConfig_CalculateChecksum(config);
    
    return 1;
}

// Функция вычисления контрольной суммы
uint16_t MotorConfig_CalculateChecksum(const AllMotorsConfig_t* config)
{
    if (!config) return 0;
    
    uint16_t checksum = 0;
    const uint8_t* data = (const uint8_t*)config;
    
    // Вычисляем контрольную сумму всех данных кроме самого поля checksum
    for (uint32_t i = 0; i < sizeof(AllMotorsConfig_t) - sizeof(config->checksum); i++) {
        checksum += data[i];
    }
    
    return checksum;
}

// Функция проверки конфигурации
uint8_t MotorConfig_ValidateConfig(const AllMotorsConfig_t* config)
{
    if (!config) return 0;
    
    // Проверяем версию
    if (config->config_version != CONFIG_VERSION) return 0;
    
    // Проверяем контрольную сумму
    uint16_t calculated_checksum = MotorConfig_CalculateChecksum(config);
    if (calculated_checksum != config->checksum) return 0;
    
    // Проверяем разумные пределы для параметров моторов
    const MotorConfig_t* motors[] = {&config->M1, &config->M2, &config->M3, 
                                     &config->M4, &config->M5, &config->M6, &config->M7};
    
    for (int i = 0; i < 7; i++) {
        // Проверяем задержки (от 10 мкс до 10000 мкс)
        if (motors[i]->step_delay_us < 10 || motors[i]->step_delay_us > 10000) return 0;
        if (motors[i]->pulse_width_us < 5 || motors[i]->pulse_width_us > 5000) return 0;
        
        // Проверяем скорость (от 100 до 50000 шагов/сек)
        if (motors[i]->max_speed_steps_sec < 100 || motors[i]->max_speed_steps_sec > 50000) return 0;
    }
    
    return 1;
}

// Заглушка для загрузки с SD карты (пока из Flash)
uint8_t MotorConfig_LoadFromSD(AllMotorsConfig_t* config)
{
    if (!config) return 0;
    
    LCD_Send_Command(LCD_4_LINE_POS_0);
    LCD_SendString((uint8_t *)"Loading config...   ", 20);
    
    // Пока читаем из Flash памяти
    const AllMotorsConfig_t* flash_config = (const AllMotorsConfig_t*)CONFIG_FLASH_ADDRESS;
    
    // Проверяем, есть ли валидная конфигурация во Flash
    if (MotorConfig_ValidateConfig(flash_config)) {
        memcpy(config, flash_config, sizeof(AllMotorsConfig_t));
        LCD_Send_Command(LCD_4_LINE_POS_0);
        LCD_SendString((uint8_t *)"Config loaded OK    ", 20);
        return 1;
    } else {
        // Загружаем значения по умолчанию
        MotorConfig_LoadDefaults(config);
        LCD_Send_Command(LCD_4_LINE_POS_0);
        LCD_SendString((uint8_t *)"Default config used ", 20);
        return 1;
    }
}

// Заглушка для сохранения на SD карту (пока во Flash)
uint8_t MotorConfig_SaveToSD(const AllMotorsConfig_t* config)
{
    if (!config) return 0;
    
    LCD_Send_Command(LCD_4_LINE_POS_0);
    LCD_SendString((uint8_t *)"Saving config...    ", 20);
    
    // Проверяем конфигурацию перед сохранением
    if (!MotorConfig_ValidateConfig(config)) {
        LCD_Send_Command(LCD_4_LINE_POS_0);
        LCD_SendString((uint8_t *)"Config invalid!     ", 20);
        return 0;
    }
    
    // Здесь должна быть реальная запись во Flash
    // Пока просто имитируем успешное сохранение
    
    LCD_Send_Command(LCD_4_LINE_POS_0);
    LCD_SendString((uint8_t *)"Config saved OK     ", 20);
    
    return 1;
}

// Функция сохранения в текстовый файл (для удобства редактирования)
uint8_t MotorConfig_SaveToTXT(const AllMotorsConfig_t* config)
{
    // Здесь будет код для создания текстового файла motor_config.txt
    // с параметрами в удобочитаемом формате
    return 1;
}

// Функция загрузки из текстового файла
uint8_t MotorConfig_LoadFromTXT(AllMotorsConfig_t* config)
{
    // Здесь будет код для чтения текстового файла motor_config.txt
    // и парсинга параметров
    return 1;
}

// Функция инициализации системы конфигурации
void MotorConfig_Init(void)
{
    LCD_Send_Command(LCD_4_LINE_POS_0);
    LCD_SendString((uint8_t *)"Motor config init...", 20);
    
    // Пытаемся загрузить конфигурацию
    if (!MotorConfig_LoadFromSD(&g_MotorConfig)) {
        // Если не удалось, загружаем значения по умолчанию
        MotorConfig_LoadDefaults(&g_MotorConfig);
        LCD_Send_Command(LCD_4_LINE_POS_0);
        LCD_SendString((uint8_t *)"Using defaults      ", 20);
    }
    
    Delay_mS(500);
}
