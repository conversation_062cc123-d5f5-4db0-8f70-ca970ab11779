
#include "IO_gpio.h" 
#include "stm32f10x.h"

void SetupGpioIO(void)
 {
	 AFIO->MAPR |= AFIO_MAPR_SWJ_CFG_JTAGDISABLE;
//	 AFIO->MAPR |= AFIO_MAPR_SWJ_CFG_DISABLE;	 
	 PWR->CSR &= ~PWR_CSR_EWUP; //Disable Wake Up Pin (PA0)
	 
	              //INPUTS:
//Set up GPIOs for inputs:
	 
	 //Keyboard: SW1, SW2, SW3, SW4, SW5, SW6:
/*******************************************************/	 
   //PB8    SW1:
    GPIOB->CRH &= ~GPIO_CRH_MODE8; //Input Mode, Reset state
	  GPIOB->CRH |= GPIO_CRH_CNF8_0; //01: Floating input (reset state)
		GPIOB->ODR |= GPIO_ODR_ODR8;

   //PB9    SW2:
    GPIOB->CRH &= ~GPIO_CRH_MODE9; //Input Mode, Reset state
	  GPIOB->CRH |= GPIO_CRH_CNF9_0; //01: Floating input (reset state)
    GPIOB->ODR |= GPIO_ODR_ODR9;
	 
   //PB12    SW3:
    GPIOB->CRH &= ~GPIO_CRH_MODE12; //Input Mode, Reset state
	  GPIOB->CRH |= GPIO_CRH_CNF12_0; //01: Floating input (reset state)
    GPIOB->ODR |= GPIO_ODR_ODR12;
		
    //PB13    SW4:
    GPIOB->CRH &= ~GPIO_CRH_MODE13; //Input Mode, Reset state
	  GPIOB->CRH |= GPIO_CRH_CNF13_0; //01: Floating input (reset state)
		GPIOB->ODR |= GPIO_ODR_ODR13;
		
     //PB14   SW5:
    GPIOB->CRH &= ~GPIO_CRH_MODE14; //Input Mode, Reset state
	  GPIOB->CRH |= GPIO_CRH_CNF14_0; //01: Floating input (reset state)	
		GPIOB->ODR |= GPIO_ODR_ODR14;
		
     //PB15   SW6: 
    GPIOB->CRH &= ~GPIO_CRH_MODE15; //Input Mode, Reset state
	  GPIOB->CRH |= GPIO_CRH_CNF15_0; //01: Floating input (reset state)
    GPIOB->ODR |= GPIO_ODR_ODR15;
/*******************************************************/		
		
//Digital sensors (D1 - D14):	
/*******************************************************/
    //PE0    D1:
    GPIOE->CRL &= ~GPIO_CRL_MODE0; //Input Mode, Reset state
	  GPIOE->CRL |= GPIO_CRL_CNF0_0; //01: Floating input (reset state)
	  GPIOE->ODR |= GPIO_ODR_ODR0;
		
    //PE1   D2:
    GPIOE->CRL &= ~GPIO_CRL_MODE1; //Input Mode, Reset state
	  GPIOE->CRL |= GPIO_CRL_CNF1_0; //01: Floating input (reset state)
	  GPIOE->ODR |= GPIO_ODR_ODR1;
		
    //PE2   D3:
    GPIOE->CRL &= ~GPIO_CRL_MODE2; //Input Mode, Reset state
	  GPIOE->CRL |= GPIO_CRL_CNF2_0; //01: Floating input (reset state)
	  GPIOE->ODR |= GPIO_ODR_ODR2;	

    //PE3   D4:
    GPIOE->CRL &= ~GPIO_CRL_MODE3; //Input Mode, Reset state
	  GPIOE->CRL |= GPIO_CRL_CNF3_0; //01: Floating input (reset state)
	  GPIOE->ODR |= GPIO_ODR_ODR3;
		
    //PE4   D5:
    GPIOE->CRL &= ~GPIO_CRL_MODE4; //Input Mode, Reset state
	  GPIOE->CRL |= GPIO_CRL_CNF4_0; //01: Floating input (reset state)
	  GPIOE->ODR |= GPIO_ODR_ODR4;	

    //PE5   D6:
    GPIOE->CRL &= ~GPIO_CRL_MODE5; //Input Mode, Reset state
	  GPIOE->CRL |= GPIO_CRL_CNF5_0; //01: Floating input (reset state)
	  GPIOE->ODR |= GPIO_ODR_ODR5;
		
    //PE6   D7:
    GPIOE->CRL &= ~GPIO_CRL_MODE6; //Input Mode, Reset state
	  GPIOE->CRL |= GPIO_CRL_CNF6_0; //01: Floating input (reset state)
	  GPIOE->ODR |= GPIO_ODR_ODR6;

    //PE7   D8:
    GPIOE->CRL &= ~GPIO_CRL_MODE7; //Input Mode, Reset state
	  GPIOE->CRL |= GPIO_CRL_CNF7_0; //01: Floating input (reset state)
	  GPIOE->ODR |= GPIO_ODR_ODR7;
		
    //PE8   D9:
    GPIOE->CRH &= ~GPIO_CRH_MODE8; //Input Mode, Reset state
	  GPIOE->CRH |= GPIO_CRH_CNF8_0; //01: Floating input (reset state)
	  GPIOE->ODR |= GPIO_ODR_ODR8;	

    //PE9   D10:
    GPIOE->CRH &= ~GPIO_CRH_MODE9; //Input Mode, Reset state
	  GPIOE->CRH |= GPIO_CRH_CNF9_0; //01: Floating input (reset state)
	  GPIOE->ODR |= GPIO_ODR_ODR9;
		
    //PE10   D11:
    GPIOE->CRH &= ~GPIO_CRH_MODE10; //Input Mode, Reset state
	  GPIOE->CRH |= GPIO_CRH_CNF10_0; //01: Floating input (reset state)
	  GPIOE->ODR |= GPIO_ODR_ODR10;		
		
    //PE11   D12:
    GPIOE->CRH &= ~GPIO_CRH_MODE11; //Input Mode, Reset state
	  GPIOE->CRH |= GPIO_CRH_CNF11_0; //01: Floating input (reset state)
	  GPIOE->ODR |= GPIO_ODR_ODR11;		
		
    //PE12   D13:
    GPIOE->CRH &= ~GPIO_CRH_MODE12; //Input Mode, Reset state
	  GPIOE->CRH |= GPIO_CRH_CNF12_0; //01: Floating input (reset state)
	  GPIOE->ODR |= GPIO_ODR_ODR12;		
		
    //PE13   D14: 
    GPIOE->CRH &= ~GPIO_CRH_MODE13; //Input Mode, Reset state
	  GPIOE->CRH |= GPIO_CRH_CNF13_0; //01: Floating input (reset state)
	  GPIOE->ODR |= GPIO_ODR_ODR13;
/*******************************************************/		
		
//Driver Alarms (ALARM_1 - ALARM_7):	
/*******************************************************/
    //PA4    ALARM_1:
    GPIOA->CRL &= ~GPIO_CRL_MODE4; //Input Mode, Reset state
	  GPIOA->CRL |= GPIO_CRL_CNF4_0; //01: Floating input (reset state)
	  GPIOA->ODR |= GPIO_ODR_ODR4;	

    //    ALARM_2:
    //GPIOA->CRL &= ~GPIO_CRL_MODE5; //Input Mode, Reset state
	  //GPIOA->CRL |= GPIO_CRL_CNF5_0; //01: Floating input (reset state)
	  //GPIOA->ODR |= GPIO_ODR_ODR5;
		
    //PA6    ALARM_3:
    GPIOA->CRL &= ~GPIO_CRL_MODE6; //Input Mode, Reset state
	  GPIOA->CRL |= GPIO_CRL_CNF6_0; //01: Floating input (reset state)
	  GPIOA->ODR |= GPIO_ODR_ODR6;

    //PA7    ALARM_4:
    GPIOA->CRL &= ~GPIO_CRL_MODE7; //Input Mode, Reset state
	  GPIOA->CRL |= GPIO_CRL_CNF7_0; //01: Floating input (reset state)
	  GPIOA->ODR |= GPIO_ODR_ODR7;
		
    //PA8    ALARM_5:
    GPIOA->CRH &= ~GPIO_CRH_MODE8; //Input Mode, Reset state
	  GPIOA->CRH |= GPIO_CRH_CNF8_0; //01: Floating input (reset state)
	  GPIOA->ODR |= GPIO_ODR_ODR8;		
		
    //PA11    ALARM_6:
    GPIOA->CRL &= ~GPIO_CRH_MODE11; //Input Mode, Reset state
	  GPIOA->CRL |= GPIO_CRH_CNF11_0; //01: Floating input (reset state)
	  GPIOA->ODR |= GPIO_ODR_ODR11;		
		
    //PA12    ALARM_7:
    GPIOA->CRL &= ~GPIO_CRH_MODE12; //Input Mode, Reset state
	  GPIOA->CRL |= GPIO_CRH_CNF12_0; //01: Floating input (reset state)
	  GPIOA->ODR |= GPIO_ODR_ODR12;	
/*******************************************************/		

//Encoders E_1, E_2 Outputs (E_0 - E_9, E10, E11 - reserved):	
/*******************************************************/
    //PD0    E_0:
    GPIOD->CRL &= ~GPIO_CRL_MODE0; //Input Mode, Reset state
	  GPIOD->CRL |= GPIO_CRL_CNF0_0; //01: Floating input (reset state)
	  GPIOD->ODR |= GPIO_ODR_ODR0;
		
    //PD1    E_1:
    GPIOD->CRL &= ~GPIO_CRL_MODE1; //Input Mode, Reset state
	  GPIOD->CRL |= GPIO_CRL_CNF1_0; //01: Floating input (reset state)
	  GPIOD->ODR |= GPIO_ODR_ODR1;	

    //PD2    E_2:
    GPIOD->CRL &= ~GPIO_CRL_MODE2; //Input Mode, Reset state
	  GPIOD->CRL |= GPIO_CRL_CNF2_0; //01: Floating input (reset state)
	  GPIOD->ODR |= GPIO_ODR_ODR2;
		
    //PD3    E_3:
    GPIOD->CRL &= ~GPIO_CRL_MODE3; //Input Mode, Reset state
	  GPIOD->CRL |= GPIO_CRL_CNF3_0; //01: Floating input (reset state)
	  GPIOD->ODR |= GPIO_ODR_ODR3;

    //PD4    E_4:
    GPIOD->CRL &= ~GPIO_CRL_MODE4; //Input Mode, Reset state
	  GPIOD->CRL |= GPIO_CRL_CNF4_0; //01: Floating input (reset state)
	  GPIOD->ODR |= GPIO_ODR_ODR4;
		
    //PD5    E_5:
    GPIOD->CRL &= ~GPIO_CRL_MODE5; //Input Mode, Reset state
	  GPIOD->CRL |= GPIO_CRL_CNF5_0; //01: Floating input (reset state)
	  GPIOD->ODR |= GPIO_ODR_ODR5;

    //PD6    E_6:
    GPIOD->CRL &= ~GPIO_CRL_MODE6; //Input Mode, Reset state
	  GPIOD->CRL |= GPIO_CRL_CNF6_0; //01: Floating input (reset state)
	  GPIOD->ODR |= GPIO_ODR_ODR6;
		
    //PD7    E_7:
    GPIOD->CRL &= ~GPIO_CRL_MODE7; //Input Mode, Reset state
	  GPIOD->CRL |= GPIO_CRL_CNF7_0; //01: Floating input (reset state)
	  GPIOD->ODR |= GPIO_ODR_ODR7;

    //PD8    E_8:
    GPIOD->CRH &= ~GPIO_CRH_MODE8; //Input Mode, Reset state
	  GPIOD->CRH |= GPIO_CRH_CNF8_0; //01: Floating input (reset state)
	  GPIOD->ODR |= GPIO_ODR_ODR8;
		
    //PD9    E_9:
    GPIOD->CRH &= ~GPIO_CRH_MODE9; //Input Mode, Reset state
	  GPIOD->CRH |= GPIO_CRH_CNF9_0; //01: Floating input (reset state)
	  GPIOD->ODR |= GPIO_ODR_ODR9;	

    //PD10    E_10: (Reserved)
    GPIOD->CRH &= ~GPIO_CRH_MODE10; //Input Mode, Reset state
	  GPIOD->CRH |= GPIO_CRH_CNF10_0; //01: Floating input (reset state)
	  GPIOD->ODR |= GPIO_ODR_ODR10;
		
    //PD11    E_11: (Reserved)
    GPIOD->CRH &= ~GPIO_CRH_MODE11; //Input Mode, Reset state
	  GPIOD->CRH |= GPIO_CRH_CNF11_0; //01: Floating input (reset state)
	  GPIOD->ODR |= GPIO_ODR_ODR11;		
/*******************************************************/

//Analog Inputs for I and U sensors:
/*******************************************************/
 //PA0	 Analog Input for Motor M7 Current:
    GPIOA->CRL &= ~GPIO_CRL_MODE0; //Input Mode, Reset state
    GPIOA->CRL &= ~GPIO_CRL_CNF0; //00: Analog input
	 
 //PA1  Analog Input for Battarey Voltage:
    GPIOA->CRL &= ~GPIO_CRL_MODE1; //Input Mode, Reset state
    GPIOA->CRL &= ~GPIO_CRL_CNF1; //00: Analog input
/*******************************************************/

	              //OUTPUTS:
//Set up GPIOs for outputs:

//Motor Controls:
/*******************************************************/
//PB0     STEP:              
    GPIOB->CRL |= GPIO_CRL_MODE0_0; //Output at 10 MHz
    GPIOB->CRL &= ~GPIO_CRL_CNF0; //Clear CRL[1:0] buts, 00: General purpose output push-pull
		GPIOB->ODR &= (~GPIO_ODR_ODR0);//Write '0' to PB0

//PB1     DIR:               
    GPIOB->CRL |= GPIO_CRL_MODE1_0; //Output at 10 MHz
    GPIOB->CRL &= ~GPIO_CRL_CNF1; //Clear CRL[1:0] buts, 00: General purpose output push-pull
		GPIOB->ODR &= (~GPIO_ODR_ODR1);//Write '0' to PB1
		
//PB2	 ENABLE:
    GPIOB->CRL |= GPIO_CRL_MODE2_0; //Output at 10 MHz
    GPIOB->CRL &= ~GPIO_CRL_CNF2; //Clear CRL[1:0] buts, 00: General purpose output push-pull
		GPIOB->ODR &= (~GPIO_ODR_ODR2);//Write '0' to PB2	 
/*******************************************************/

//Motors Enables:
/*******************************************************/
//PB3   E_0:                
    GPIOB->CRL |= GPIO_CRL_MODE3_0; //Output at 10 MHz
    GPIOB->CRL &= ~GPIO_CRL_CNF3; //Clear CRL[1:0] buts, 00: General purpose output push-pull
		GPIOB->ODR &= (~GPIO_ODR_ODR3);//Write '0' to PB3

//PB4   E_1:
    GPIOB->CRL |= GPIO_CRL_MODE4_0; //Output at 10 MHz
	  GPIOB->CRL &= ~GPIO_CRL_CNF4; //Clear CRL[1:0] bits, , 00: General purpose output push-pull
		GPIOB->ODR &= (~GPIO_ODR_ODR4); //Write '0'		
		
//PB5    E_2:    
    GPIOB->CRL |= GPIO_CRL_MODE5_0; //Output at 10 MHz
	  GPIOB->CRL &= ~GPIO_CRL_CNF5; //Clear CRL[1:0] bits, 00: General purpose output push-pull
		GPIOB->ODR &= (~GPIO_ODR_ODR5); //Write '0' to PB5		
/*******************************************************/

//Encoders Enable:
/*******************************************************/
//PD12   Encoder_1 Enable:
    GPIOD->CRH |= GPIO_CRH_MODE12_0; //Output at 10 MHz
	  GPIOD->CRH &= ~GPIO_CRH_CNF12; //Clear CRL[1:0] bits, , 00: General purpose output push-pull
		GPIOD->ODR |= GPIO_ODR_ODR12; //Write '1'	, Encoder_1 Disbled

//PD13   Encoder_2 Enable:
    GPIOD->CRH |= GPIO_CRH_MODE13_0; //Output at 10 MHz
	  GPIOD->CRH &= ~GPIO_CRH_CNF13; //Clear CRL[1:0] bits, , 00: General purpose output push-pull
		GPIOD->ODR |= GPIO_ODR_ODR13; //Write '1', Encoder_2 Disbled
		
//PC13 On-board LED
    GPIOC->CRH |= GPIO_CRH_MODE13_0; //Output at 10 MHz
	  GPIOC->CRH &= ~GPIO_CRH_CNF13; //Clear CRL[1:0] bits, , 00: General purpose output push-pull
		//GPIOC->ODR &= (~GPIO_ODR_ODR13); //Write '0'
		GPIOC->ODR |= GPIO_ODR_ODR13; //Write '1'
		
//PC0 BEEP
    GPIOC->CRL |= GPIO_CRL_MODE0_0; //Output at 10 MHz
	  GPIOC->CRL &= ~GPIO_CRL_CNF0; //Clear CRL[1:0] bits, , 00: General purpose output push-pull
		//GPIOC->ODR &= (~GPIO_ODR_ODR13); //Write '0'
		GPIOC->ODR &= ~GPIO_ODR_ODR0; //Write '0'
		
//PC1  DD16_Enble
    GPIOC->CRL |= GPIO_CRL_MODE1_0; //Output at 10 MHz
	  GPIOC->CRL &= ~GPIO_CRL_CNF1; //Clear CRL[1:0] bits, , 00: General purpose output push-pull
		//GPIOC->ODR &= (~GPIO_ODR_ODR1); //Write '0'
		GPIOC->ODR &= ~GPIO_ODR_ODR1; //Write '0'
		
//PC2  (M7 GO Left Control)
    //GPIOC->CRL |= GPIO_CRL_MODE2_0; //Output at 10 MHz
	  //GPIOC->CRL &= ~GPIO_CRL_CNF2; //Clear CRL[1:0] bits, , 00: General purpose output push-pull
		//GPIOC->ODR &= (~GPIO_ODR_ODR1); //Write '0'
		//GPIOC->ODR &= ~GPIO_ODR_ODR2; //Write '0'		
		
		//PC3  M7 GO Right Control:
    GPIOC->CRL |= GPIO_CRL_MODE3_0; //Output at 10 MHz
	  GPIOC->CRL &= ~(GPIO_CRL_CNF3); //Clear CRL[1:0] bits, , 00: General purpose output push-pull
		GPIOC->ODR &= ~(GPIO_ODR_ODR3); //Write '0'
		
		//PA5  M7 GO Left  Control:
    //GPIOA->CRL |= GPIO_CRL_MODE5_0; //Output at 10 MHz
	  //GPIOA->CRL &= ~(GPIO_CRL_CNF5); //01: Floating output 
	  //GPIOA->ODR &= ~(GPIO_ODR_ODR5);	
		
    GPIOB->CRH |= GPIO_CRH_MODE10_0; //Output Mode
	  GPIOB->CRH &= ~(GPIO_CRH_CNF10);  //01: Floating output 
		GPIOB->ODR &= ~(GPIO_ODR_ODR10);
//
    //GPIOC->CRL |= GPIO_CRL_MODE4_0; //Output at 10 MHz
	  //GPIOC->CRL &= ~GPIO_CRL_CNF4; //Clear CRL[1:0] bits, , 00: General purpose output push-pull
		//GPIOC->ODR &= (~GPIO_ODR_ODR1); //Write '0'
		//GPIOC->ODR &= ~GPIO_ODR_ODR4; //Write '0'

// PC5 M7 (GO Left Control)
    //GPIOC->CRL |= GPIO_CRL_MODE5_0; //Output at 10 MHz
	  //GPIOC->CRL &= ~GPIO_CRL_CNF5; //Clear CRL[1:0] bits, , 00: General purpose output push-pull
		//GPIOC->ODR &= (~GPIO_ODR_ODR1); //Write '0'
		//GPIOC->ODR &= ~GPIO_ODR_ODR5; //Write '0'		
/*******************************************************/

            //Set up GPIOs for Interfaces:

//Set up GPIO for UART1 (RS485 driver):
/*******************************************************/
//PA9  UART1 Tx, push-pull output in alternative mode, 10 MHz
	GPIOA->CRH |= GPIO_CRH_MODE9_0; //Output at 10 MHz, Mode[1:0] = 0x01 
  GPIOA->CRH &= ~GPIO_CRH_CNF9; // Clear CNF bits 1:0
	GPIOA->CRH |= GPIO_CRH_CNF9_1; //CNF[1:0] = 0x10 (AFIO Push-Pull)

//PA10 UART1 Rx
	GPIOA->CRH	&= ~GPIO_CRH_MODE10;	//Input(Reset state), MODE[1:0] = 0x00
	GPIOA->CRH	&= ~GPIO_CRH_CNF10;	// Clear CNF bits 1:0
  GPIOA->CRH	|= GPIO_CRH_CNF10_1;	//CNF[1:0] = 0x10, Input with pull-up/pull-down
  GPIOA->ODR  |= GPIO_ODR_ODR10;  //Set up pull-up

//PA15   For RS485_2 Rx/Tx Control
    GPIOA->CRH |= GPIO_CRH_MODE15_0; //Output at 10 MHz
    GPIOA->CRH &= ~GPIO_CRH_CNF15; //CRL[1:0] buts, 00: General purpose output push-pull
		GPIOA->ODR &= ~(GPIO_ODR_ODR15);    //Write '0' to PA15
/*******************************************************/


//Set up GPIO for UART2:
/*******************************************************/
  //PA2  UART2 Tx, push-pull output in alternative mode, 10 MHz
	GPIOA->CRL |= GPIO_CRL_MODE2_0; //Output at 10 MHz, Mode[1:0] = 0x01 
  GPIOA->CRL &= ~GPIO_CRL_CNF2; // Clear CNF bits 1:0
	GPIOA->CRL |= GPIO_CRL_CNF2_1; //CNF[1:0] = 0x10 (AFIO Push-Pull)
	
//PA3 UART2 Rx
	GPIOA->CRL	&= ~GPIO_CRL_MODE3;	//Input(Reset state), MODE[1:0] = 0x00
	GPIOA->CRL	&= ~GPIO_CRL_CNF3;	// Clear CNF bits 1:0
  GPIOA->CRL	|= GPIO_CRL_CNF3_1;	//CNF[1:0] = 0x10, Input with pull-up/pull-down
  GPIOA->ODR  |= GPIO_ODR_ODR3;  //Set up pull-up
/*******************************************************/	

//I2C1 Interface:
/*******************************************************/
		//PB6 I2C1 SCL - Open Drain Output:
    GPIOB->CRL |= GPIO_CRL_MODE6_0; //Output Mode
	  GPIOB->CRL |= GPIO_CRL_CNF6;  //01: Open Drain AF
		
		//PB7 I2C1 SDA - Open Drain Output:
    GPIOB->CRL |= GPIO_CRL_MODE7_0; //Output at 10 MHz
	  GPIOB->CRL |= GPIO_CRL_CNF7;  //01: Open Drain AF

/*******************************************************/

//I2C2 Interface:
/*******************************************************/
		//PB10 I2C2 SCL - Open Drain Output:
    //GPIOB->CRH |= GPIO_CRH_MODE10_0; //Output Mode
	  //GPIOB->CRH |= GPIO_CRH_CNF10;  //01: Open Drain AF

		//PB11 I2C2 SDA - Open Drain Output:
    //GPIOB->CRH |= GPIO_CRH_MODE10_0; //Output at 10 MHz
	  //GPIOB->CRH |= GPIO_CRH_CNF10;  //01: Open Drain AF

/*******************************************************/

}//End OF SetupGpioIO(void)
 
 