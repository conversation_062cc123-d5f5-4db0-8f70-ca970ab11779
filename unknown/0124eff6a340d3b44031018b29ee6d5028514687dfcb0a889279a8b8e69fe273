#ifndef MOTOR_DIAGNOSTICS_H
#define MOTOR_DIAGNOSTICS_H

#include <stdint.h>

// =================================================================
// СИСТЕМА ДИАГНОСТИКИ И МОНИТОРИНГА МОТОРОВ CORDON-82
// Автоматическое обнаружение проблем и предиктивное обслуживание
// =================================================================

// Максимальное количество моторов для диагностики
#define MAX_MOTORS_DIAG 7

// Уровни здоровья мотора
#define MOTOR_HEALTH_EXCELLENT  90  // 90-100% - отличное состояние
#define MOTOR_HEALTH_GOOD       70  // 70-89% - хорошее состояние  
#define MOTOR_HEALTH_FAIR       50  // 50-69% - удовлетворительное
#define MOTOR_HEALTH_POOR       30  // 30-49% - плохое состояние
#define MOTOR_HEALTH_CRITICAL   10  // 0-29% - критическое состояние

// Типы событий для логирования
#define EVENT_MOTOR_START       1   // Запуск мотора
#define EVENT_MOTOR_STOP        2   // Остановка мотора
#define EVENT_SENSOR_REACHED    3   // Датчик достигнут
#define EVENT_TIMEOUT           4   // Таймаут
#define EVENT_ERROR             5   // Ошибка
#define EVENT_MAINTENANCE       6   // Требуется обслуживание
#define EVENT_CALIBRATION       7   // Калибровка
#define EVENT_SPEED_CHANGE      8   // Изменение скорости

// Коды ошибок
#define ERROR_NONE              0   // Нет ошибок
#define ERROR_TIMEOUT           1   // Таймаут движения
#define ERROR_SENSOR_FAIL       2   // Отказ датчика
#define ERROR_OVERCURRENT       3   // Превышение тока
#define ERROR_OVERHEAT          4   // Перегрев
#define ERROR_MECHANICAL        5   // Механическая проблема
#define ERROR_COMMUNICATION     6   // Ошибка связи
#define ERROR_CALIBRATION       7   // Ошибка калибровки

// =================================================================
// СТРУКТУРЫ ДАННЫХ ДЛЯ ДИАГНОСТИКИ
// =================================================================

// Структура события в логе
typedef struct {
    uint32_t timestamp;         // Время события (мс от старта)
    uint8_t motor_id;           // ID мотора (1-7)
    uint8_t event_type;         // Тип события
    uint8_t error_code;         // Код ошибки (если есть)
    uint16_t additional_data;   // Дополнительные данные
} motor_event_t;

// Расширенная статистика мотора
typedef struct {
    // Основные счетчики
    uint32_t total_operations;      // Общее количество операций
    uint32_t successful_operations; // Успешные операции
    uint32_t failed_operations;     // Неудачные операции
    uint32_t total_steps;           // Общее количество шагов
    uint32_t total_runtime_ms;      // Общее время работы (мс)
    
    // Статистика производительности
    uint16_t avg_operation_time;    // Среднее время операции (мс)
    uint16_t max_operation_time;    // Максимальное время операции (мс)
    uint16_t min_operation_time;    // Минимальное время операции (мс)
    uint32_t last_operation_time;   // Время последней операции (мс)
    
    // Статистика ошибок
    uint16_t timeout_count;         // Количество таймаутов
    uint16_t sensor_error_count;    // Ошибки датчиков
    uint16_t mechanical_error_count;// Механические ошибки
    uint16_t other_error_count;     // Прочие ошибки
    
    // Показатели здоровья
    uint8_t health_score;           // Общий показатель здоровья (0-100)
    uint8_t reliability_score;      // Показатель надежности (0-100)
    uint8_t performance_score;      // Показатель производительности (0-100)
    uint8_t maintenance_score;      // Потребность в обслуживании (0-100)
    
    // Прогнозирование
    uint16_t predicted_failures;    // Прогнозируемые отказы
    uint32_t next_maintenance_ms;   // Время следующего ТО (мс)
    uint8_t wear_level;             // Уровень износа (0-100)
    uint8_t stress_level;           // Уровень нагрузки (0-100)
    
    // Температурные данные (если доступны)
    uint8_t current_temp;           // Текущая температура
    uint8_t max_temp;               // Максимальная температура
    uint8_t avg_temp;               // Средняя температура
    uint8_t temp_warnings;          // Количество предупреждений о температуре
    
    // Временные метки
    uint32_t first_start_time;      // Время первого запуска
    uint32_t last_activity_time;    // Время последней активности
    uint32_t last_maintenance_time; // Время последнего ТО
    uint32_t creation_time;         // Время создания записи
} motor_diagnostics_t;

// Структура системной диагностики
typedef struct {
    uint32_t system_uptime_ms;      // Время работы системы
    uint32_t total_operations;      // Общее количество операций
    uint32_t system_errors;         // Системные ошибки
    uint8_t system_health;          // Общее здоровье системы (0-100)
    uint8_t active_motors;          // Количество активных моторов
    uint8_t motors_need_maintenance;// Моторы, требующие ТО
    uint32_t last_full_check;       // Время последней полной проверки
    uint16_t event_log_count;       // Количество событий в логе
} system_diagnostics_t;

// =================================================================
// ГЛОБАЛЬНЫЕ ПЕРЕМЕННЫЕ
// =================================================================

// Диагностические данные для каждого мотора
extern motor_diagnostics_t motor_diag[MAX_MOTORS_DIAG];

// Системная диагностика
extern system_diagnostics_t system_diag;

// Лог событий (кольцевой буфер)
#define EVENT_LOG_SIZE 50
extern motor_event_t event_log[EVENT_LOG_SIZE];
extern uint8_t event_log_head;
extern uint8_t event_log_count;

// =================================================================
// ФУНКЦИИ ДИАГНОСТИКИ
// =================================================================

// Инициализация системы диагностики
void Diagnostics_Init(void);

// Основные функции логирования
void Log_Motor_Event(uint8_t motor_id, uint8_t event_type, uint8_t error_code, uint16_t data);
void Log_Motor_Start(uint8_t motor_id);
void Log_Motor_Stop(uint8_t motor_id, uint8_t success);
void Log_Motor_Error(uint8_t motor_id, uint8_t error_code);

// Обновление статистики
void Update_Motor_Performance(uint8_t motor_id, uint16_t operation_time, uint8_t success);
void Update_Motor_Health(uint8_t motor_id);
void Calculate_Health_Scores(uint8_t motor_id);

// Анализ и прогнозирование
uint8_t Analyze_Motor_Health(uint8_t motor_id);
uint8_t Predict_Motor_Failure(uint8_t motor_id);
uint32_t Calculate_Next_Maintenance(uint8_t motor_id);
uint8_t Check_Motor_Stress(uint8_t motor_id);

// Отображение диагностики
void Show_Motor_Diagnostics(uint8_t motor_id);
void Show_System_Diagnostics(void);
void Show_Motor_Health_Summary(void);
void Show_Event_Log(void);
void Show_Maintenance_Schedule(void);

// Автоматические проверки
void Perform_System_Health_Check(void);
void Check_All_Motors_Health(void);
void Generate_Health_Report(void);
void Check_Maintenance_Schedule(void);

// Управление данными
void Reset_Motor_Statistics(uint8_t motor_id);
void Reset_All_Statistics(void);
void Export_Diagnostics_Data(void);
void Clear_Event_Log(void);

// Предупреждения и алерты
void Check_Critical_Conditions(void);
void Generate_Maintenance_Alert(uint8_t motor_id);
void Generate_Health_Warning(uint8_t motor_id);
void Generate_System_Alert(uint8_t alert_type);

// Утилиты
uint32_t Get_System_Uptime(void);
uint8_t Get_Motor_Status(uint8_t motor_id);
const char* Get_Error_Description(uint8_t error_code);
const char* Get_Health_Description(uint8_t health_score);

// Автоматические функции (вызываются периодически)
void Diagnostics_Background_Task(void);
void Update_System_Health(void);
void Periodic_Health_Check(void);

#endif // MOTOR_DIAGNOSTICS_H
