#ifndef MOTOR_CONFIG_H
#define MOTOR_CONFIG_H

#include <stdint.h>

// Структура для хранения параметров одного мотора
typedef struct {
    uint16_t step_delay_us;        // Задержка между шагами в микросекундах
    uint16_t pulse_width_us;       // Длительность импульса в микросекундах
    uint16_t acceleration_steps;   // Количество шагов для разгона
    uint16_t max_speed_steps_sec;  // Максимальная скорость в шагах/сек
    uint8_t  enable_acceleration;  // Включить разгон (0/1)
    uint8_t  direction_invert;     // Инвертировать направление (0/1)
    uint8_t  enable_motor;         // Включить мотор (0/1)
    uint8_t  reserved;             // Резерв
} MotorConfig_t;

// Структура для всех моторов
typedef struct {
    MotorConfig_t M1;  // Мотор горизонтальной наводки
    MotorConfig_t M2;  // Мотор вертикальной наводки  
    MotorConfig_t M3;  // Мотор каретки
    MotorConfig_t M4;  // Мотор продольного перемещения механизма загрузки
    MotorConfig_t M5;  // Мотор вращения механизма загрузки
    MotorConfig_t M6;  // Мотор вращения барабана
    MotorConfig_t M7;  // Мотор сжатия и разжатия механизма захвата
    
    // Общие параметры
    uint16_t config_version;       // Версия конфигурации
    uint16_t checksum;             // Контрольная сумма
    uint32_t last_update;          // Время последнего обновления
    char     description[32];      // Описание конфигурации
} AllMotorsConfig_t;

// Значения по умолчанию
#define DEFAULT_M1_STEP_DELAY       100    // 5 кГц
#define DEFAULT_M1_PULSE_WIDTH      50     // 50 мкс
#define DEFAULT_M1_MAX_SPEED        5000   // 5000 шагов/сек

#define DEFAULT_M2_STEP_DELAY       100    // 5 кГц  
#define DEFAULT_M2_PULSE_WIDTH      50     // 50 мкс
#define DEFAULT_M2_MAX_SPEED        5000   // 5000 шагов/сек

#define DEFAULT_M3_STEP_DELAY       100    // 5 кГц
#define DEFAULT_M3_PULSE_WIDTH      50     // 50 мкс
#define DEFAULT_M3_MAX_SPEED        5000   // 5000 шагов/сек

#define DEFAULT_M4_STEP_DELAY       100    // 5 кГц
#define DEFAULT_M4_PULSE_WIDTH      50     // 50 мкс
#define DEFAULT_M4_MAX_SPEED        5000   // 5000 шагов/сек

#define DEFAULT_M5_STEP_DELAY       100    // 5 кГц
#define DEFAULT_M5_PULSE_WIDTH      50     // 50 мкс
#define DEFAULT_M5_MAX_SPEED        5000   // 5000 шагов/сек

#define DEFAULT_M6_STEP_DELAY       200    // Максимальная мощность
#define DEFAULT_M6_PULSE_WIDTH      100    // 100 мкс
#define DEFAULT_M6_MAX_SPEED        2500   // 2500 шагов/сек

#define DEFAULT_M7_STEP_DELAY       1000   // DC мотор - не используется
#define DEFAULT_M7_PULSE_WIDTH      500    // DC мотор - не используется  
#define DEFAULT_M7_MAX_SPEED        1000   // DC мотор - не используется

// Имя файла конфигурации на SD карте
#define CONFIG_FILE_NAME            "motor_config.bin"
#define CONFIG_FILE_NAME_TXT        "motor_config.txt"
#define CONFIG_VERSION              0x0001

// Функции для работы с конфигурацией
uint8_t MotorConfig_LoadFromSD(AllMotorsConfig_t* config);
uint8_t MotorConfig_SaveToSD(const AllMotorsConfig_t* config);
uint8_t MotorConfig_LoadDefaults(AllMotorsConfig_t* config);
uint8_t MotorConfig_ValidateConfig(const AllMotorsConfig_t* config);
uint16_t MotorConfig_CalculateChecksum(const AllMotorsConfig_t* config);
uint8_t MotorConfig_SaveToTXT(const AllMotorsConfig_t* config);
uint8_t MotorConfig_LoadFromTXT(AllMotorsConfig_t* config);
void MotorConfig_Init(void);

// Глобальная переменная конфигурации
extern AllMotorsConfig_t g_MotorConfig;

// Макросы для быстрого доступа к параметрам
#define M1_STEP_DELAY()     (g_MotorConfig.M1.step_delay_us)
#define M1_PULSE_WIDTH()    (g_MotorConfig.M1.pulse_width_us)
#define M1_MAX_SPEED()      (g_MotorConfig.M1.max_speed_steps_sec)

#define M2_STEP_DELAY()     (g_MotorConfig.M2.step_delay_us)
#define M2_PULSE_WIDTH()    (g_MotorConfig.M2.pulse_width_us)
#define M2_MAX_SPEED()      (g_MotorConfig.M2.max_speed_steps_sec)

#define M3_STEP_DELAY()     (g_MotorConfig.M3.step_delay_us)
#define M3_PULSE_WIDTH()    (g_MotorConfig.M3.pulse_width_us)
#define M3_MAX_SPEED()      (g_MotorConfig.M3.max_speed_steps_sec)

#define M4_STEP_DELAY()     (g_MotorConfig.M4.step_delay_us)
#define M4_PULSE_WIDTH()    (g_MotorConfig.M4.pulse_width_us)
#define M4_MAX_SPEED()      (g_MotorConfig.M4.max_speed_steps_sec)

#define M5_STEP_DELAY()     (g_MotorConfig.M5.step_delay_us)
#define M5_PULSE_WIDTH()    (g_MotorConfig.M5.pulse_width_us)
#define M5_MAX_SPEED()      (g_MotorConfig.M5.max_speed_steps_sec)

#define M6_STEP_DELAY()     (g_MotorConfig.M6.step_delay_us)
#define M6_PULSE_WIDTH()    (g_MotorConfig.M6.pulse_width_us)
#define M6_MAX_SPEED()      (g_MotorConfig.M6.max_speed_steps_sec)

#define M7_STEP_DELAY()     (g_MotorConfig.M7.step_delay_us)
#define M7_PULSE_WIDTH()    (g_MotorConfig.M7.pulse_width_us)
#define M7_MAX_SPEED()      (g_MotorConfig.M7.max_speed_steps_sec)

#endif /* MOTOR_CONFIG_H */
