#include "motor_diagnostics.h"
#include "UserFunction.h"
#include "main.h"

// =================================================================
// ГЛОБАЛЬНЫЕ ПЕРЕМЕННЫЕ ДИАГНОСТИКИ
// =================================================================

// Диагностические данные для каждого мотора
motor_diagnostics_t motor_diag[MAX_MOTORS_DIAG];

// Системная диагностика
system_diagnostics_t system_diag;

// Лог событий (кольцевой буфер)
motor_event_t event_log[EVENT_LOG_SIZE];
uint8_t event_log_head = 0;
uint8_t event_log_count = 0;

// Счетчик времени системы (простая реализация)
static uint32_t system_time_ms = 0;

// =================================================================
// ИНИЦИАЛИЗАЦИЯ СИСТЕМЫ ДИАГНОСТИКИ
// =================================================================
void Diagnostics_Init(void) {
    // Инициализация диагностических данных моторов
    for(uint8_t i = 0; i < MAX_MOTORS_DIAG; i++) {
        motor_diag[i].total_operations = 0;
        motor_diag[i].successful_operations = 0;
        motor_diag[i].failed_operations = 0;
        motor_diag[i].total_steps = 0;
        motor_diag[i].total_runtime_ms = 0;
        
        motor_diag[i].avg_operation_time = 0;
        motor_diag[i].max_operation_time = 0;
        motor_diag[i].min_operation_time = 65535; // Максимальное значение
        motor_diag[i].last_operation_time = 0;
        
        motor_diag[i].timeout_count = 0;
        motor_diag[i].sensor_error_count = 0;
        motor_diag[i].mechanical_error_count = 0;
        motor_diag[i].other_error_count = 0;
        
        motor_diag[i].health_score = 100;
        motor_diag[i].reliability_score = 100;
        motor_diag[i].performance_score = 100;
        motor_diag[i].maintenance_score = 0;
        
        motor_diag[i].predicted_failures = 0;
        motor_diag[i].next_maintenance_ms = 0;
        motor_diag[i].wear_level = 0;
        motor_diag[i].stress_level = 0;
        
        motor_diag[i].current_temp = 25;
        motor_diag[i].max_temp = 25;
        motor_diag[i].avg_temp = 25;
        motor_diag[i].temp_warnings = 0;
        
        motor_diag[i].first_start_time = 0;
        motor_diag[i].last_activity_time = 0;
        motor_diag[i].last_maintenance_time = 0;
        motor_diag[i].creation_time = Get_System_Uptime();
    }
    
    // Инициализация системной диагностики
    system_diag.system_uptime_ms = 0;
    system_diag.total_operations = 0;
    system_diag.system_errors = 0;
    system_diag.system_health = 100;
    system_diag.active_motors = 6; // M1-M6 активны
    system_diag.motors_need_maintenance = 0;
    system_diag.last_full_check = 0;
    system_diag.event_log_count = 0;
    
    // Очистка лога событий
    event_log_head = 0;
    event_log_count = 0;
    
    // Логирование инициализации
    Log_Motor_Event(0, EVENT_MOTOR_START, ERROR_NONE, 0);
    
    LCD_Send_Command(LCD_4_LINE_POS_0);
    LCD_SendString((uint8_t *)"Diagnostics init OK ", 20);
}

// =================================================================
// ФУНКЦИИ ЛОГИРОВАНИЯ СОБЫТИЙ
// =================================================================
void Log_Motor_Event(uint8_t motor_id, uint8_t event_type, uint8_t error_code, uint16_t data) {
    // Добавление события в кольцевой буфер
    event_log[event_log_head].timestamp = Get_System_Uptime();
    event_log[event_log_head].motor_id = motor_id;
    event_log[event_log_head].event_type = event_type;
    event_log[event_log_head].error_code = error_code;
    event_log[event_log_head].additional_data = data;
    
    // Обновление указателей кольцевого буфера
    event_log_head = (event_log_head + 1) % EVENT_LOG_SIZE;
    if(event_log_count < EVENT_LOG_SIZE) {
        event_log_count++;
    }
    
    // Обновление системной статистики
    system_diag.event_log_count++;
    if(error_code != ERROR_NONE) {
        system_diag.system_errors++;
    }
}

void Log_Motor_Start(uint8_t motor_id) {
    if(motor_id >= MAX_MOTORS_DIAG) return;
    
    Log_Motor_Event(motor_id, EVENT_MOTOR_START, ERROR_NONE, 0);
    
    // Обновление статистики мотора
    motor_diag[motor_id].total_operations++;
    motor_diag[motor_id].last_activity_time = Get_System_Uptime();
    
    // Установка времени первого запуска
    if(motor_diag[motor_id].first_start_time == 0) {
        motor_diag[motor_id].first_start_time = Get_System_Uptime();
    }
    
    // Обновление системной статистики
    system_diag.total_operations++;
}

void Log_Motor_Stop(uint8_t motor_id, uint8_t success) {
    if(motor_id >= MAX_MOTORS_DIAG) return;
    
    uint8_t error_code = success ? ERROR_NONE : ERROR_TIMEOUT;
    Log_Motor_Event(motor_id, EVENT_MOTOR_STOP, error_code, success);
    
    // Обновление статистики
    if(success) {
        motor_diag[motor_id].successful_operations++;
    } else {
        motor_diag[motor_id].failed_operations++;
        motor_diag[motor_id].timeout_count++;
    }
    
    motor_diag[motor_id].last_activity_time = Get_System_Uptime();
    
    // Пересчет показателей здоровья
    Calculate_Health_Scores(motor_id);
}

void Log_Motor_Error(uint8_t motor_id, uint8_t error_code) {
    if(motor_id >= MAX_MOTORS_DIAG) return;
    
    Log_Motor_Event(motor_id, EVENT_ERROR, error_code, 0);
    
    // Обновление счетчиков ошибок
    switch(error_code) {
        case ERROR_TIMEOUT:
            motor_diag[motor_id].timeout_count++;
            break;
        case ERROR_SENSOR_FAIL:
            motor_diag[motor_id].sensor_error_count++;
            break;
        case ERROR_MECHANICAL:
            motor_diag[motor_id].mechanical_error_count++;
            break;
        default:
            motor_diag[motor_id].other_error_count++;
            break;
    }
    
    motor_diag[motor_id].failed_operations++;
    
    // Пересчет показателей здоровья
    Calculate_Health_Scores(motor_id);
}

// =================================================================
// РАСЧЕТ ПОКАЗАТЕЛЕЙ ЗДОРОВЬЯ
// =================================================================
void Calculate_Health_Scores(uint8_t motor_id) {
    if(motor_id >= MAX_MOTORS_DIAG) return;
    
    motor_diagnostics_t* diag = &motor_diag[motor_id];
    
    // Расчет показателя надежности (на основе успешности операций)
    if(diag->total_operations > 0) {
        diag->reliability_score = (diag->successful_operations * 100) / diag->total_operations;
    } else {
        diag->reliability_score = 100;
    }
    
    // Расчет показателя производительности (на основе времени операций)
    if(diag->avg_operation_time > 0) {
        // Чем меньше время, тем лучше производительность
        if(diag->avg_operation_time < 1000) {
            diag->performance_score = 100;
        } else if(diag->avg_operation_time < 5000) {
            diag->performance_score = 80;
        } else if(diag->avg_operation_time < 10000) {
            diag->performance_score = 60;
        } else {
            diag->performance_score = 40;
        }
    } else {
        diag->performance_score = 100;
    }
    
    // Расчет потребности в обслуживании
    uint32_t total_errors = diag->timeout_count + diag->sensor_error_count + 
                           diag->mechanical_error_count + diag->other_error_count;
    
    if(total_errors == 0) {
        diag->maintenance_score = 0;
    } else if(total_errors < 5) {
        diag->maintenance_score = 20;
    } else if(total_errors < 10) {
        diag->maintenance_score = 50;
    } else {
        diag->maintenance_score = 100;
    }
    
    // Расчет общего показателя здоровья
    diag->health_score = (diag->reliability_score + diag->performance_score) / 2;
    
    // Снижение здоровья при высокой потребности в обслуживании
    if(diag->maintenance_score > 50) {
        diag->health_score = (diag->health_score * 70) / 100;
    }
    
    // Ограничение диапазона
    if(diag->health_score > 100) diag->health_score = 100;
    if(diag->health_score < 0) diag->health_score = 0;
}

// =================================================================
// ФУНКЦИИ ОТОБРАЖЕНИЯ ДИАГНОСТИКИ
// =================================================================
void Show_Motor_Diagnostics(uint8_t motor_id) {
    if(motor_id >= MAX_MOTORS_DIAG) return;
    
    motor_diagnostics_t* diag = &motor_diag[motor_id];
    
    LCD_Send_Command(LCD_1_LINE_POS_0);
    LCD_SendString((uint8_t *)"=== MOTOR DIAG ===  ", 20);
    
    LCD_Send_Command(LCD_2_LINE_POS_0);
    char line2[21];
    if(motor_id == 0) {
        LCD_SendString((uint8_t *)"SYS: System Status  ", 20);
    } else {
        LCD_SendString((uint8_t *)"M", 1);
        LCD_SendString((uint8_t *)&motor_id, 1);
        LCD_SendString((uint8_t *)": Motor Status    ", 17);
    }
    
    LCD_Send_Command(LCD_3_LINE_POS_0);
    if(diag->health_score >= MOTOR_HEALTH_EXCELLENT) {
        LCD_SendString((uint8_t *)"Health: EXCELLENT   ", 20);
    } else if(diag->health_score >= MOTOR_HEALTH_GOOD) {
        LCD_SendString((uint8_t *)"Health: GOOD        ", 20);
    } else if(diag->health_score >= MOTOR_HEALTH_FAIR) {
        LCD_SendString((uint8_t *)"Health: FAIR        ", 20);
    } else if(diag->health_score >= MOTOR_HEALTH_POOR) {
        LCD_SendString((uint8_t *)"Health: POOR        ", 20);
    } else {
        LCD_SendString((uint8_t *)"Health: CRITICAL    ", 20);
    }
    
    LCD_Send_Command(LCD_4_LINE_POS_0);
    if(diag->total_operations > 1000) {
        LCD_SendString((uint8_t *)"Ops: MANY (>1000)   ", 20);
    } else if(diag->total_operations > 100) {
        LCD_SendString((uint8_t *)"Ops: MODERATE       ", 20);
    } else if(diag->total_operations > 0) {
        LCD_SendString((uint8_t *)"Ops: FEW            ", 20);
    } else {
        LCD_SendString((uint8_t *)"Ops: NONE           ", 20);
    }
    
    // Программная задержка для отображения
    for(uint32_t i = 0; i < 1000000; i++) {}
}

void Show_System_Diagnostics(void) {
    LCD_Send_Command(LCD_1_LINE_POS_0);
    LCD_SendString((uint8_t *)"=== SYSTEM DIAG === ", 20);
    
    LCD_Send_Command(LCD_2_LINE_POS_0);
    if(system_diag.system_health >= 90) {
        LCD_SendString((uint8_t *)"System: EXCELLENT   ", 20);
    } else if(system_diag.system_health >= 70) {
        LCD_SendString((uint8_t *)"System: GOOD        ", 20);
    } else if(system_diag.system_health >= 50) {
        LCD_SendString((uint8_t *)"System: FAIR        ", 20);
    } else {
        LCD_SendString((uint8_t *)"System: POOR        ", 20);
    }
    
    LCD_Send_Command(LCD_3_LINE_POS_0);
    if(system_diag.motors_need_maintenance == 0) {
        LCD_SendString((uint8_t *)"Maintenance: OK     ", 20);
    } else if(system_diag.motors_need_maintenance == 1) {
        LCD_SendString((uint8_t *)"Maintenance: 1 motor", 20);
    } else {
        LCD_SendString((uint8_t *)"Maintenance: MULTI  ", 20);
    }
    
    LCD_Send_Command(LCD_4_LINE_POS_0);
    if(system_diag.system_errors == 0) {
        LCD_SendString((uint8_t *)"Errors: NONE        ", 20);
    } else if(system_diag.system_errors < 10) {
        LCD_SendString((uint8_t *)"Errors: FEW         ", 20);
    } else {
        LCD_SendString((uint8_t *)"Errors: MANY        ", 20);
    }
    
    // Программная задержка для отображения
    for(uint32_t i = 0; i < 1000000; i++) {}
}

// =================================================================
// УТИЛИТЫ
// =================================================================
uint32_t Get_System_Uptime(void) {
    // Простая реализация - увеличиваем счетчик
    system_time_ms++;
    return system_time_ms;
}

const char* Get_Health_Description(uint8_t health_score) {
    if(health_score >= MOTOR_HEALTH_EXCELLENT) return "EXCELLENT";
    if(health_score >= MOTOR_HEALTH_GOOD) return "GOOD";
    if(health_score >= MOTOR_HEALTH_FAIR) return "FAIR";
    if(health_score >= MOTOR_HEALTH_POOR) return "POOR";
    return "CRITICAL";
}

const char* Get_Error_Description(uint8_t error_code) {
    switch(error_code) {
        case ERROR_NONE: return "NO ERROR";
        case ERROR_TIMEOUT: return "TIMEOUT";
        case ERROR_SENSOR_FAIL: return "SENSOR FAIL";
        case ERROR_OVERCURRENT: return "OVERCURRENT";
        case ERROR_OVERHEAT: return "OVERHEAT";
        case ERROR_MECHANICAL: return "MECHANICAL";
        case ERROR_COMMUNICATION: return "COMM ERROR";
        case ERROR_CALIBRATION: return "CALIBRATION";
        default: return "UNKNOWN";
    }
}

// =================================================================
// РАСШИРЕННЫЕ ФУНКЦИИ ДИАГНОСТИКИ
// =================================================================

void Show_Motor_Health_Summary(void) {
    LCD_Send_Command(LCD_1_LINE_POS_0);
    LCD_SendString((uint8_t *)"=== HEALTH SUMMARY ==", 20);

    // Подсчет моторов по состоянию здоровья
    uint8_t excellent = 0, good = 0, fair = 0, poor = 0, critical = 0;

    for(uint8_t i = 1; i < MAX_MOTORS_DIAG; i++) {
        if(motor_diag[i].health_score >= MOTOR_HEALTH_EXCELLENT) excellent++;
        else if(motor_diag[i].health_score >= MOTOR_HEALTH_GOOD) good++;
        else if(motor_diag[i].health_score >= MOTOR_HEALTH_FAIR) fair++;
        else if(motor_diag[i].health_score >= MOTOR_HEALTH_POOR) poor++;
        else critical++;
    }

    LCD_Send_Command(LCD_2_LINE_POS_0);
    if(critical > 0) {
        LCD_SendString((uint8_t *)"Status: CRITICAL!   ", 20);
    } else if(poor > 0) {
        LCD_SendString((uint8_t *)"Status: POOR        ", 20);
    } else if(fair > 2) {
        LCD_SendString((uint8_t *)"Status: FAIR        ", 20);
    } else if(good > 0) {
        LCD_SendString((uint8_t *)"Status: GOOD        ", 20);
    } else {
        LCD_SendString((uint8_t *)"Status: EXCELLENT   ", 20);
    }

    LCD_Send_Command(LCD_3_LINE_POS_0);
    if(excellent >= 5) {
        LCD_SendString((uint8_t *)"Motors: ALL HEALTHY ", 20);
    } else if(excellent >= 3) {
        LCD_SendString((uint8_t *)"Motors: MOSTLY OK   ", 20);
    } else {
        LCD_SendString((uint8_t *)"Motors: NEED CARE   ", 20);
    }

    LCD_Send_Command(LCD_4_LINE_POS_0);
    if(critical > 0 || poor > 1) {
        LCD_SendString((uint8_t *)"Action: MAINTENANCE!", 20);
    } else if(fair > 2) {
        LCD_SendString((uint8_t *)"Action: CHECK SOON  ", 20);
    } else {
        LCD_SendString((uint8_t *)"Action: CONTINUE    ", 20);
    }
}

void Perform_System_Health_Check(void) {
    LCD_Send_Command(LCD_1_LINE_POS_0);
    LCD_SendString((uint8_t *)"=== HEALTH CHECK ===", 20);

    // Обновление показателей здоровья всех моторов
    for(uint8_t i = 1; i < MAX_MOTORS_DIAG; i++) {
        Calculate_Health_Scores(i);
    }

    // Подсчет общего здоровья системы
    uint16_t total_health = 0;
    uint8_t active_motors = 0;

    for(uint8_t i = 1; i < MAX_MOTORS_DIAG; i++) {
        if(motor_diag[i].total_operations > 0) {
            total_health += motor_diag[i].health_score;
            active_motors++;
        }
    }

    if(active_motors > 0) {
        system_diag.system_health = total_health / active_motors;
    } else {
        system_diag.system_health = 100;
    }

    system_diag.active_motors = active_motors;
    system_diag.last_full_check = Get_System_Uptime();

    LCD_Send_Command(LCD_2_LINE_POS_0);
    LCD_SendString((uint8_t *)"Checking motors...  ", 20);

    // Программная задержка
    for(uint32_t i = 0; i < 500000; i++) {}

    LCD_Send_Command(LCD_3_LINE_POS_0);
    if(system_diag.system_health >= 90) {
        LCD_SendString((uint8_t *)"Result: EXCELLENT   ", 20);
    } else if(system_diag.system_health >= 70) {
        LCD_SendString((uint8_t *)"Result: GOOD        ", 20);
    } else if(system_diag.system_health >= 50) {
        LCD_SendString((uint8_t *)"Result: FAIR        ", 20);
    } else {
        LCD_SendString((uint8_t *)"Result: POOR        ", 20);
    }

    LCD_Send_Command(LCD_4_LINE_POS_0);
    LCD_SendString((uint8_t *)"Health check done   ", 20);

    // Программная задержка
    for(uint32_t i = 0; i < 500000; i++) {}
}

void Check_All_Motors_Health(void) {
    LCD_Send_Command(LCD_1_LINE_POS_0);
    LCD_SendString((uint8_t *)"=== MOTOR CHECK === ", 20);

    for(uint8_t motor_id = 1; motor_id <= 6; motor_id++) {
        LCD_Send_Command(LCD_2_LINE_POS_0);
        LCD_SendString((uint8_t *)"Checking M", 10);
        LCD_SendString((uint8_t *)&motor_id, 1);
        LCD_SendString((uint8_t *)"...       ", 9);

        // Программная задержка для имитации проверки
        for(uint32_t i = 0; i < 200000; i++) {}

        Calculate_Health_Scores(motor_id);

        LCD_Send_Command(LCD_3_LINE_POS_0);
        if(motor_diag[motor_id].health_score >= MOTOR_HEALTH_EXCELLENT) {
            LCD_SendString((uint8_t *)"M", 1);
            LCD_SendString((uint8_t *)&motor_id, 1);
            LCD_SendString((uint8_t *)": EXCELLENT      ", 17);
        } else if(motor_diag[motor_id].health_score >= MOTOR_HEALTH_GOOD) {
            LCD_SendString((uint8_t *)"M", 1);
            LCD_SendString((uint8_t *)&motor_id, 1);
            LCD_SendString((uint8_t *)": GOOD           ", 17);
        } else if(motor_diag[motor_id].health_score >= MOTOR_HEALTH_FAIR) {
            LCD_SendString((uint8_t *)"M", 1);
            LCD_SendString((uint8_t *)&motor_id, 1);
            LCD_SendString((uint8_t *)": FAIR           ", 17);
        } else {
            LCD_SendString((uint8_t *)"M", 1);
            LCD_SendString((uint8_t *)&motor_id, 1);
            LCD_SendString((uint8_t *)": NEEDS SERVICE  ", 17);
        }

        // Программная задержка для отображения
        for(uint32_t i = 0; i < 300000; i++) {}
    }

    LCD_Send_Command(LCD_4_LINE_POS_0);
    LCD_SendString((uint8_t *)"All motors checked  ", 20);
}

void Update_Motor_Performance(uint8_t motor_id, uint16_t operation_time, uint8_t success) {
    if(motor_id >= MAX_MOTORS_DIAG) return;

    motor_diagnostics_t* diag = &motor_diag[motor_id];

    // Обновление времени операции
    diag->last_operation_time = operation_time;

    // Обновление минимального и максимального времени
    if(operation_time < diag->min_operation_time) {
        diag->min_operation_time = operation_time;
    }
    if(operation_time > diag->max_operation_time) {
        diag->max_operation_time = operation_time;
    }

    // Расчет среднего времени операции
    if(diag->total_operations > 0) {
        diag->avg_operation_time = ((uint32_t)diag->avg_operation_time * (diag->total_operations - 1) + operation_time) / diag->total_operations;
    } else {
        diag->avg_operation_time = operation_time;
    }

    // Обновление общего времени работы
    diag->total_runtime_ms += operation_time;

    // Логирование производительности
    if(success) {
        Log_Motor_Event(motor_id, EVENT_MOTOR_STOP, ERROR_NONE, operation_time);
    } else {
        Log_Motor_Event(motor_id, EVENT_TIMEOUT, ERROR_TIMEOUT, operation_time);
    }
}
