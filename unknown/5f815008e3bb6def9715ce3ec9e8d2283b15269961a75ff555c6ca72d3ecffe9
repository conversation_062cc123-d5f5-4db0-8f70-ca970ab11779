#include "motor_unified_config.h"
#include "UserFunction.h"
#include "main.h"

// Определения для совместимости
#define NULL ((void*)0)

// =================================================================
// ГЛОБАЛЬНЫЕ МАССИВЫ КОНФИГУРАЦИИ И СТАТИСТИКИ
// =================================================================

// Конфигурация всех моторов
motor_config_t motors[MAX_MOTORS];

// Статистика всех моторов  
motor_stats_t motor_stats[MAX_MOTORS];

// =================================================================
// ИНИЦИАЛИЗАЦИЯ УНИФИЦИРОВАННОЙ СИСТЕМЫ
// =================================================================
void Motor_System_Init(void) {
    // M0 - НЕ ИСПОЛЬЗУЕТСЯ
    motors[0] = (motor_config_t){
        .step_delay_us = M0_STEP_DELAY_US,
        .pulse_width_us = M0_PULSE_WIDTH_US,
        .extra_delay_us = 0,
        .max_speed_hz = M0_MAX_SPEED_HZ,
        .max_steps = M0_MAX_STEPS,
        .progress_step = 0,
        .enabled = M0_ENABLED,
        .motor_type = 0,
        .has_encoder = 0,
        .reverse_direction = 0,
        .name = "M0",
        .description = "Not used"
    };
    
    // M1 - МОТОР ГОРИЗОНТАЛЬНОЙ НАВОДКИ
    motors[1] = (motor_config_t){
        .step_delay_us = M1_STEP_DELAY_US,
        .pulse_width_us = M1_PULSE_WIDTH_US,
        .extra_delay_us = M1_EXTRA_DELAY_US,
        .max_speed_hz = M1_MAX_SPEED_HZ,
        .max_steps = M1_MAX_STEPS,
        .progress_step = M1_PROGRESS_STEP,
        .enabled = M1_ENABLED,
        .motor_type = M1_MOTOR_TYPE,
        .has_encoder = M1_HAS_ENCODER,
        .reverse_direction = M1_REVERSE_DIR,
        .name = "M1",
        .description = "Horizontal aiming"
    };
    
    // M2 - МОТОР ВЕРТИКАЛЬНОЙ НАВОДКИ
    motors[2] = (motor_config_t){
        .step_delay_us = M2_STEP_DELAY_US,
        .pulse_width_us = M2_PULSE_WIDTH_US,
        .extra_delay_us = M2_EXTRA_DELAY_US,
        .max_speed_hz = M2_MAX_SPEED_HZ,
        .max_steps = M2_MAX_STEPS,
        .progress_step = M2_PROGRESS_STEP,
        .enabled = M2_ENABLED,
        .motor_type = M2_MOTOR_TYPE,
        .has_encoder = M2_HAS_ENCODER,
        .reverse_direction = M2_REVERSE_DIR,
        .name = "M2",
        .description = "Vertical aiming"
    };
    
    // M3 - МОТОР КАРЕТКИ
    motors[3] = (motor_config_t){
        .step_delay_us = M3_STEP_DELAY_US,
        .pulse_width_us = M3_PULSE_WIDTH_US,
        .extra_delay_us = M3_EXTRA_DELAY_US,
        .max_speed_hz = M3_MAX_SPEED_HZ,
        .max_steps = M3_MAX_STEPS,
        .progress_step = M3_PROGRESS_STEP,
        .enabled = M3_ENABLED,
        .motor_type = M3_MOTOR_TYPE,
        .has_encoder = M3_HAS_ENCODER,
        .reverse_direction = M3_REVERSE_DIR,
        .name = "M3",
        .description = "Carriage lift"
    };
    
    // M4 - МОТОР ПРОДОЛЬНОГО ПЕРЕМЕЩЕНИЯ
    motors[4] = (motor_config_t){
        .step_delay_us = M4_STEP_DELAY_US,
        .pulse_width_us = M4_PULSE_WIDTH_US,
        .extra_delay_us = M4_EXTRA_DELAY_US,
        .max_speed_hz = M4_MAX_SPEED_HZ,
        .max_steps = M4_MAX_STEPS,
        .progress_step = M4_PROGRESS_STEP,
        .enabled = M4_ENABLED,
        .motor_type = M4_MOTOR_TYPE,
        .has_encoder = M4_HAS_ENCODER,
        .reverse_direction = M4_REVERSE_DIR,
        .name = "M4",
        .description = "Longitudinal move"
    };
    
    // M5 - МОТОР ВРАЩЕНИЯ МЕХАНИЗМА ЗАГРУЗКИ
    motors[5] = (motor_config_t){
        .step_delay_us = M5_STEP_DELAY_US,
        .pulse_width_us = M5_PULSE_WIDTH_US,
        .extra_delay_us = M5_EXTRA_DELAY_US,
        .max_speed_hz = M5_MAX_SPEED_HZ,
        .max_steps = M5_MAX_STEPS,
        .progress_step = M5_PROGRESS_STEP,
        .enabled = M5_ENABLED,
        .motor_type = M5_MOTOR_TYPE,
        .has_encoder = M5_HAS_ENCODER,
        .reverse_direction = M5_REVERSE_DIR,
        .name = "M5",
        .description = "Loading mechanism"
    };
    
    // M6 - МОТОР ВРАЩЕНИЯ БАРАБАНА
    motors[6] = (motor_config_t){
        .step_delay_us = M6_STEP_DELAY_US,
        .pulse_width_us = M6_PULSE_WIDTH_US,
        .extra_delay_us = M6_EXTRA_DELAY_US,
        .max_speed_hz = M6_MAX_SPEED_HZ,
        .max_steps = M6_MAX_STEPS,
        .progress_step = M6_PROGRESS_STEP,
        .enabled = M6_ENABLED,
        .motor_type = M6_MOTOR_TYPE,
        .has_encoder = M6_HAS_ENCODER,
        .reverse_direction = M6_REVERSE_DIR,
        .name = "M6",
        .description = "Drum rotation"
    };
    
    // M7 - DC МОТОР ЗАХВАТА
    motors[7] = (motor_config_t){
        .step_delay_us = M7_STEP_DELAY_US,
        .pulse_width_us = M7_PULSE_WIDTH_US,
        .extra_delay_us = M7_EXTRA_DELAY_US,
        .max_speed_hz = M7_MAX_SPEED_HZ,
        .max_steps = M7_MAX_STEPS,
        .progress_step = M7_PROGRESS_STEP,
        .enabled = M7_ENABLED,
        .motor_type = M7_MOTOR_TYPE,
        .has_encoder = M7_HAS_ENCODER,
        .reverse_direction = M7_REVERSE_DIR,
        .name = "M7",
        .description = "Gripper DC motor"
    };
    
    // Инициализация статистики
    for(uint8_t i = 0; i < MAX_MOTORS; i++) {
        motor_stats[i].total_steps = 0;
        motor_stats[i].successful_moves = 0;
        motor_stats[i].timeouts = 0;
        motor_stats[i].errors = 0;
        motor_stats[i].last_move_time = 0;
        motor_stats[i].last_move_steps = 0;
        motor_stats[i].health_status = 100;
        motor_stats[i].current_position = 0;
        motor_stats[i].last_direction = 0;
        motor_stats[i].temperature = 25;
    }
    
    LCD_Send_Command(LCD_4_LINE_POS_0);
    LCD_SendString((uint8_t *)"Motor system init OK", 20);
}

// =================================================================
// УНИВЕРСАЛЬНАЯ ФУНКЦИЯ УПРАВЛЕНИЯ МОТОРОМ
// =================================================================
uint8_t Rotate_Motor_Universal(uint8_t motor_id, uint8_t direction, uint8_t target_sensor) {
    // Проверка валидности параметров
    if(motor_id >= MAX_MOTORS || !motors[motor_id].enabled) {
        return MOTOR_STATUS_ERROR;
    }
    
    motor_config_t* motor = &motors[motor_id];
    uint16_t step_count = 0;
    uint8_t sensor_reached = 0;
    
    // Отображение начала движения
    LCD_Send_Command(LCD_4_LINE_POS_0);
    LCD_SendString((uint8_t *)motor->name, 8);
    LCD_SendString((uint8_t *)": Starting...   ", 12);
    
    // Выбор и инициализация мотора (используем существующие функции)
    switch(motor_id) {
        case 1: Choose_M1; break;
        case 2: Choose_M2; break;
        case 3: Choose_M3; break;
        case 4: Choose_M4; break;
        case 5: Choose_M5; break;
        case 6: Choose_M6; break;
        default: return MOTOR_STATUS_ERROR;
    }
    
    DD16_Enble;
    Delay_mS(5);
    Enable_Motor;
    
    // Установка направления
    if(direction == MOTOR_FORWARD) {
        Rotate_CW;
    } else {
        Rotate_CCW;
    }
    
    // Задержка инициализации
    for(uint16_t t = 0; t < 1000; t++) {}
    
    // Основной цикл движения с таймаутом
    while(step_count < motor->max_steps) {
        // Выполнение шага мотора
        GPIOB->ODR |= GPIO_ODR_ODR0;
        Delay_uS(motor->step_delay_us);
        GPIOB->ODR &= (~GPIO_ODR_ODR0);
        Delay_uS(motor->pulse_width_us);
        
        // Дополнительная задержка для M2
        if(motor_id == 2 && motor->extra_delay_us > 0) {
            Delay_uS(motor->extra_delay_us);
        }
        
        step_count++;
        
        // Проверка достижения целевого датчика
        sensor_reached = Check_Target_Sensor(target_sensor);
        if(sensor_reached) {
            LCD_Send_Command(LCD_4_LINE_POS_0);
            LCD_SendString((uint8_t *)motor->name, 8);
            LCD_SendString((uint8_t *)": Target OK     ", 12);
            break;
        }
        
        // Индикация прогресса
        if(motor->progress_step > 0 && (step_count % motor->progress_step) == 0) {
            LCD_Send_Command(LCD_4_LINE_POS_0);
            LCD_SendString((uint8_t *)motor->name, 8);
            LCD_SendString((uint8_t *)": Moving...     ", 12);
        }
    }
    
    // Остановка мотора
    Disable_Motor;
    DD16_Disble;
    
    // Определение результата
    uint8_t result;
    if(sensor_reached) {
        result = MOTOR_STATUS_OK;
        Update_Motor_Stats_New(motor_id, 1, step_count);
    } else {
        result = MOTOR_STATUS_TIMEOUT;
        LCD_Send_Command(LCD_4_LINE_POS_0);
        LCD_SendString((uint8_t *)motor->name, 8);
        LCD_SendString((uint8_t *)": TIMEOUT!      ", 12);
        Update_Motor_Stats_New(motor_id, 0, step_count);
        SensorPositionError = 1;
    }
    
    return result;
}

// =================================================================
// ФУНКЦИИ УПРАВЛЕНИЯ СТАТИСТИКОЙ
// =================================================================
void Update_Motor_Stats_New(uint8_t motor_id, uint8_t success, uint16_t steps) {
    if(motor_id >= MAX_MOTORS) return;
    
    motor_stats_t* stats = &motor_stats[motor_id];
    
    stats->total_steps += steps;
    stats->last_move_steps = steps;
    stats->last_move_time = 0; // TODO: Добавить системное время
    
    if(success) {
        stats->successful_moves++;
        // Улучшаем здоровье при успехе
        if(stats->health_status < 100) {
            stats->health_status++;
        }
    } else {
        stats->timeouts++;
        // Ухудшаем здоровье при таймауте
        if(stats->health_status > 5) {
            stats->health_status -= 5;
        }
    }
}

// =================================================================
// ФУНКЦИИ ДОСТУПА К КОНФИГУРАЦИИ
// =================================================================
motor_config_t* Get_Motor_Config(uint8_t motor_id) {
    if(motor_id >= MAX_MOTORS) return NULL;
    return &motors[motor_id];
}

motor_stats_t* Get_Motor_Stats(uint8_t motor_id) {
    if(motor_id >= MAX_MOTORS) return NULL;
    return &motor_stats[motor_id];
}

// =================================================================
// ФУНКЦИИ НАСТРОЙКИ ПАРАМЕТРОВ
// =================================================================
void Set_Motor_Speed(uint8_t motor_id, uint32_t step_delay_us) {
    if(motor_id >= MAX_MOTORS) return;
    
    motors[motor_id].step_delay_us = step_delay_us;
    motors[motor_id].pulse_width_us = step_delay_us; // Обычно равны
    
    // Пересчитываем частоту
    if(step_delay_us > 0) {
        motors[motor_id].max_speed_hz = 1000000 / (step_delay_us * 2);
    }
}

void Set_Motor_Max_Steps(uint8_t motor_id, uint16_t max_steps) {
    if(motor_id >= MAX_MOTORS) return;
    motors[motor_id].max_steps = max_steps;
}

void Enable_Motor_Config(uint8_t motor_id, uint8_t enabled) {
    if(motor_id >= MAX_MOTORS) return;
    motors[motor_id].enabled = enabled;
}

// =================================================================
// ФУНКЦИИ ДИАГНОСТИКИ И ОТОБРАЖЕНИЯ
// =================================================================
void Show_Motor_Config(uint8_t motor_id) {
    if(motor_id >= MAX_MOTORS) return;

    motor_config_t* motor = &motors[motor_id];

    LCD_Send_Command(LCD_1_LINE_POS_0);
    LCD_SendString((uint8_t *)"=== MOTOR CONFIG ===", 20);

    LCD_Send_Command(LCD_2_LINE_POS_0);
    LCD_SendString((uint8_t *)motor->name, 8);
    LCD_SendString((uint8_t *)": Config OK     ", 12);

    LCD_Send_Command(LCD_3_LINE_POS_0);
    if(motor->max_speed_hz >= 1000) {
        LCD_SendString((uint8_t *)"Speed: HIGH (>1kHz) ", 20);
    } else if(motor->max_speed_hz >= 500) {
        LCD_SendString((uint8_t *)"Speed: MEDIUM       ", 20);
    } else {
        LCD_SendString((uint8_t *)"Speed: LOW (<500Hz) ", 20);
    }

    LCD_Send_Command(LCD_4_LINE_POS_0);
    if(motor->step_delay_us >= 5000) {
        LCD_SendString((uint8_t *)"Mode: SLOW & STRONG ", 20);
    } else if(motor->step_delay_us >= 1000) {
        LCD_SendString((uint8_t *)"Mode: MEDIUM        ", 20);
    } else {
        LCD_SendString((uint8_t *)"Mode: FAST & LIGHT  ", 20);
    }

    for(uint32_t i = 0; i < 500000; i++) {} // Программная задержка
}

void Show_All_Motors_Config(void) {
    for(uint8_t i = 1; i <= 6; i++) {
        if(motors[i].enabled) {
            Show_Motor_Config(i);
        }
    }
}

void Show_Motor_Stats(uint8_t motor_id) {
    if(motor_id >= MAX_MOTORS) return;

    motor_stats_t* stats = &motor_stats[motor_id];
    motor_config_t* motor = &motors[motor_id];

    LCD_Send_Command(LCD_1_LINE_POS_0);
    LCD_SendString((uint8_t *)"=== MOTOR STATS ===", 20);

    LCD_Send_Command(LCD_2_LINE_POS_0);
    LCD_SendString((uint8_t *)motor->name, 8);
    if(stats->health_status >= 90) {
        LCD_SendString((uint8_t *)": EXCELLENT     ", 12);
    } else if(stats->health_status >= 70) {
        LCD_SendString((uint8_t *)": GOOD          ", 12);
    } else if(stats->health_status >= 50) {
        LCD_SendString((uint8_t *)": FAIR          ", 12);
    } else {
        LCD_SendString((uint8_t *)": POOR          ", 12);
    }

    LCD_Send_Command(LCD_3_LINE_POS_0);
    if(stats->total_steps > 10000) {
        LCD_SendString((uint8_t *)"Steps: MANY (>10k)  ", 20);
    } else if(stats->total_steps > 1000) {
        LCD_SendString((uint8_t *)"Steps: MODERATE     ", 20);
    } else {
        LCD_SendString((uint8_t *)"Steps: FEW (<1k)    ", 20);
    }

    LCD_Send_Command(LCD_4_LINE_POS_0);
    uint32_t total_moves = stats->successful_moves + stats->timeouts;
    if(total_moves > 0) {
        uint8_t success_rate = (stats->successful_moves * 100) / total_moves;
        if(success_rate >= 95) {
            LCD_SendString((uint8_t *)"Success: EXCELLENT  ", 20);
        } else if(success_rate >= 80) {
            LCD_SendString((uint8_t *)"Success: GOOD       ", 20);
        } else if(success_rate >= 60) {
            LCD_SendString((uint8_t *)"Success: FAIR       ", 20);
        } else {
            LCD_SendString((uint8_t *)"Success: POOR       ", 20);
        }
    } else {
        LCD_SendString((uint8_t *)"Success: NO DATA    ", 20);
    }

    for(uint32_t i = 0; i < 500000; i++) {} // Программная задержка
}

void Show_All_Motors_Stats(void) {
    for(uint8_t i = 1; i <= 6; i++) {
        if(motors[i].enabled) {
            Show_Motor_Stats(i);
        }
    }
}

// =================================================================
// ФУНКЦИИ АВТОМАТИЧЕСКОЙ ОПТИМИЗАЦИИ
// =================================================================
void Auto_Optimize_Motor_Speed(uint8_t motor_id) {
    if(motor_id >= MAX_MOTORS || !motors[motor_id].enabled) return;

    LCD_Send_Command(LCD_1_LINE_POS_0);
    LCD_SendString((uint8_t *)"=== AUTO OPTIMIZE ===", 20);

    LCD_Send_Command(LCD_2_LINE_POS_0);
    LCD_SendString((uint8_t *)motors[motor_id].name, 8);
    LCD_SendString((uint8_t *)": Testing...   ", 12);

    // Сохраняем исходные настройки
    uint32_t original_delay = motors[motor_id].step_delay_us;

    // Простой тест: попробуем ускорить на 20%
    uint32_t new_delay = (original_delay * 80) / 100;
    if(new_delay < 200) new_delay = 200; // Минимум 200 мкс

    LCD_Send_Command(LCD_3_LINE_POS_0);
    if(new_delay < 500) {
        LCD_SendString((uint8_t *)"Test: ULTRA FAST    ", 20);
    } else if(new_delay < 1000) {
        LCD_SendString((uint8_t *)"Test: FAST          ", 20);
    } else {
        LCD_SendString((uint8_t *)"Test: MEDIUM        ", 20);
    }

    // Устанавливаем новую скорость
    Set_Motor_Speed(motor_id, new_delay);

    LCD_Send_Command(LCD_4_LINE_POS_0);
    LCD_SendString((uint8_t *)"Speed increased 20% ", 20);

    // Короткая пауза
    for(uint32_t i = 0; i < 1000000; i++) {} // Программная задержка
}

// =================================================================
// ВСПОМОГАТЕЛЬНЫЕ ФУНКЦИИ
// =================================================================
uint8_t Check_Target_Sensor(uint8_t sensor_id) {
    // Проверка конкретного датчика
    switch(sensor_id) {
        case 1: return !(D1);
        case 2: return !(D2);
        case 3: return !(D3);
        case 4: return !(D4);
        case 5: return D5;
        case 6: return D6;
        case 7: return !(D7);
        case 8: return !(D8);
        case 9: return D9;
        case 10: return !(D10);
        case 11: return !(D11);
        case 12: return D12;
        case 13: return D13;
        case 14: return D14;
        default: return 0;
    }
}

void Reset_Motor_Config_To_Default(void) {
    LCD_Send_Command(LCD_4_LINE_POS_0);
    LCD_SendString((uint8_t *)"Resetting config... ", 20);

    Motor_System_Init(); // Переинициализация с дефолтными значениями

    LCD_Send_Command(LCD_4_LINE_POS_0);
    LCD_SendString((uint8_t *)"Config reset OK!    ", 20);
    for(uint32_t i = 0; i < 500000; i++) {} // Программная задержка
}
