#ifndef MAIN_H
#define MAIN_H

#endif

#include "stdint.h"
#include "I2C.h"

#define LCD_ON            0x0C//Cursor and blinking OFF
#define LCD_CUR_BLINK_ON  0x0F//LCD, Cursor and blinking ON
#define CUR_BLINK_OFF  0x0C //Cursor and blinking OFF
#define LCD_OFF           0x08
#define LCD_CLEAR_POS_0   0x01
#define LCD_CURSOR_HOME   0x02


#define LCD_1_LINE_POS_0  0x80 
#define LCD_2_LINE_POS_0  0xC0
#define LCD_3_LINE_POS_0  0x94
#define LCD_4_LINE_POS_0  0xD4

extern uint8_t LCD_Current[];
extern uint8_t LCD_Voltage[];
extern uint8_t LCD_Char0[];
extern uint8_t LCD_Char1[];
extern uint8_t LCD_Char2[];
extern uint8_t LCD_Char3[];
extern uint8_t LCD_Char4[];
extern uint8_t LCD_Char5[];

extern uint8_t Chars0[];
extern uint8_t Chars1[];
extern uint8_t Chars2[];
extern uint8_t Chars3[];
extern uint8_t Chars4[];

extern uint8_t LCD_Current[];

uint8_t LCD_Byte_Read(uint8_t);

void LCD_Send_Command(uint8_t);

void LCD_Send_4BitCmd(uint8_t);

void LCD_Send_Data(uint8_t); //Sends byte

void LCD_Setup(void);

void LCD_CursorGoToLeft(uint8_t);

void LCD_CursorGoToRight(uint8_t);

void LCD_SendString(uint8_t *, uint8_t);

void LCD_Set_CGRAM(uint8_t);

void LCD_Write_CGRAM(uint8_t *);

void LCD_UserChars(void);

void Voltage_To_LCD(uint8_t *,uint8_t *);


void Current_To_LCD(uint8_t *,uint8_t *);

void Test_1(void);
