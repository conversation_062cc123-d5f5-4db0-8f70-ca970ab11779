#include "stdint.h"



void SetupTimers(void);
void TIM4_IRQHandler(void);

//#define PWM_VALUE           80
//#define TMR_T               200
//#define DEADTIME            20
 
//#define PWM_VALUE_MAX       150
//#define PWM_VALUE_MAX       80//90

//#define PWM_VALUE_MIN       25
//#define PWM_VALUE_MIN       2  //20

#define TIM2_START       TIM2->CR1 |= TIM_CR1_CEN
#define TIM2_STOP        TIM2->CR1 &= ~TIM_CR1_CEN
//extern uint16_t TIM2_INT_Counter;
