#ifndef JSON_PARSER_H
#define JSON_PARSER_H

#include <stdint.h>

// Максимальные размеры
#define JSON_MAX_CMD_LEN        20
#define JSON_MAX_VALUE_LEN      20
#define JSON_MAX_BUFFER_SIZE    200

// Результаты парсинга
typedef enum {
    JSON_OK = 0,
    JSON_ERROR,
    JSON_INVALID_FORMAT,
    JSON_UNKNOWN_COMMAND,
    JSON_BUFFER_TOO_SMALL
} JSON_Result_t;

// Команды
typedef enum {
    CMD_UNKNOWN = 0,
    CMD_RESET,
    CMD_READY,
    CMD_ABOUT,
    CMD_PLAY_MELODY,
    CMD_FIRE,
    CMD_TEST_M6,
    CMD_TEST_ALL,
    CMD_MOVE_MOTOR,
    CMD_SET_SPEED,
    CMD_LOAD_CONFIG,
    CMD_SHOW_CONFIG,
    CMD_GET_STATUS,
    CMD_STOP_ALL,
    C<PERSON>_AIM,
    CMD_SET_POSITION,
    CMD_AUTO_CALIBRATE
} JSON_Command_t;

// Структура распарсенной команды
typedef struct {
    JSON_Command_t command;
    char motor[10];           // "M1", "M2", etc.
    int32_t steps;           // количество шагов
    int32_t speed;           // скорость
    int32_t step_delay;      // задержка между шагами
    int32_t pulse_width;     // длительность импульса
    char direction[10];      // "cw", "ccw"
    int32_t duration;        // длительность в мс
    int32_t angle;           // угол в градусах
    int32_t horizontal;      // горизонтальный угол
    int32_t vertical;        // вертикальный угол
} JSON_ParsedCommand_t;

// Функции парсера
JSON_Result_t JSON_Parse(const char* json_string, JSON_ParsedCommand_t* cmd);
JSON_Result_t JSON_ExecuteCommand(const JSON_ParsedCommand_t* cmd);
const char* JSON_GetCommandName(JSON_Command_t cmd);

// Вспомогательные функции
JSON_Command_t JSON_StringToCommand(const char* cmd_str);
int32_t JSON_GetIntValue(const char* json, const char* key);
JSON_Result_t JSON_GetStringValue(const char* json, const char* key, char* value, uint8_t max_len);

#endif /* JSON_PARSER_H */

