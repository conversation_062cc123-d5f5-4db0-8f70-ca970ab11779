#ifndef MOTOR_AUTOCALIBRATION_H
#define MOTOR_AUTOCALIBRATION_H

#include <stdint.h>
#include "motor_unified_config.h"

// =================================================================
// КОНСТАНТЫ АВТОКАЛИБРОВКИ
// =================================================================

// Диапазоны тестирования скоростей (микросекунды)
#define CALIBRATION_MIN_DELAY_US    200     // Минимальная задержка (максимальная скорость)
#define CALIBRATION_MAX_DELAY_US    5000    // Максимальная задержка (минимальная скорость)
#define CALIBRATION_STEP_US         100     // Шаг изменения задержки
#define CALIBRATION_TEST_STEPS      50      // Количество тестовых шагов

// Критерии успешности
#define CALIBRATION_SUCCESS_THRESHOLD   80  // Процент успешных тестов
#define CALIBRATION_MAX_RETRIES         3   // Максимум попыток
#define CALIBRATION_STABILITY_TIME      500 // Время стабилизации (мс)

// Коды результатов калибровки
#define CALIBRATION_SUCCESS             0   // Успешно
#define CALIBRATION_ERROR_MOTOR_ID      1   // Неверный ID мотора
#define CALIBRATION_ERROR_DISABLED      2   // Мотор отключен
#define CALIBRATION_ERROR_TIMEOUT       3   // Таймаут
#define CALIBRATION_ERROR_NO_MOVEMENT   4   // Нет движения
#define CALIBRATION_ERROR_UNSTABLE      5   // Нестабильная работа

// =================================================================
// СТРУКТУРЫ ДАННЫХ
// =================================================================

// Результат тестирования одной скорости
typedef struct {
    uint32_t delay_us;              // Тестируемая задержка
    uint8_t success_rate;           // Процент успешных тестов (0-100)
    uint16_t avg_step_time_us;      // Среднее время шага
    uint16_t max_step_time_us;      // Максимальное время шага
    uint8_t stability_score;        // Оценка стабильности (0-100)
    uint8_t temperature_rise;       // Повышение температуры
} speed_test_result_t;

// Результат полной калибровки мотора
typedef struct {
    uint8_t motor_id;               // ID мотора
    uint8_t calibration_status;     // Статус калибровки
    uint32_t optimal_delay_us;      // Оптимальная задержка
    uint32_t safe_delay_us;         // Безопасная задержка (на 20% медленнее)
    uint32_t max_delay_us;          // Максимальная рабочая задержка
    uint8_t optimal_success_rate;   // Успешность оптимальной скорости
    uint8_t total_tests;            // Общее количество тестов
    uint16_t calibration_time_ms;   // Время калибровки
    char status_message[32];        // Сообщение о статусе
} motor_calibration_result_t;

// =================================================================
// ОСНОВНЫЕ ФУНКЦИИ АВТОКАЛИБРОВКИ
// =================================================================

// Инициализация системы автокалибровки
void AutoCalibration_Init(void);

// Калибровка одного мотора
uint8_t AutoCalibrate_Motor(uint8_t motor_id, motor_calibration_result_t* result);

// Калибровка всех моторов
void AutoCalibrate_All_Motors(void);

// Быстрая калибровка (только критичные моторы)
void AutoCalibrate_Critical_Motors(void);

// =================================================================
// ФУНКЦИИ ТЕСТИРОВАНИЯ
// =================================================================

// Тест одной скорости мотора
uint8_t Test_Motor_Speed_Detailed(uint8_t motor_id, uint32_t delay_us, speed_test_result_t* result);

// Поиск оптимальной скорости бинарным поиском
uint32_t Find_Optimal_Speed_Binary(uint8_t motor_id, uint32_t min_delay, uint32_t max_delay);

// Поиск максимальной безопасной скорости
uint32_t Find_Max_Safe_Speed(uint8_t motor_id);

// Тест стабильности на заданной скорости
uint8_t Test_Speed_Stability(uint8_t motor_id, uint32_t delay_us, uint16_t test_duration_ms);

// =================================================================
// ФУНКЦИИ АНАЛИЗА И ОПТИМИЗАЦИИ
// =================================================================

// Анализ характеристик мотора
void Analyze_Motor_Performance(uint8_t motor_id);

// Автоматическая настройка параметров
void Auto_Tune_Motor_Parameters(uint8_t motor_id);

// Адаптивная калибровка под нагрузку
void Adaptive_Calibration(uint8_t motor_id, uint8_t load_level);

// =================================================================
// ФУНКЦИИ ОТОБРАЖЕНИЯ И ДИАГНОСТИКИ
// =================================================================

// Показать результаты калибровки
void Show_Calibration_Results(motor_calibration_result_t* result);

// Показать прогресс калибровки
void Show_Calibration_Progress(uint8_t motor_id, uint8_t progress_percent);

// Сохранить результаты калибровки
void Save_Calibration_Results(uint8_t motor_id, motor_calibration_result_t* result);

// Загрузить сохраненные результаты
uint8_t Load_Calibration_Results(uint8_t motor_id, motor_calibration_result_t* result);

// =================================================================
// ВСПОМОГАТЕЛЬНЫЕ ФУНКЦИИ
// =================================================================

// Проверка готовности мотора к калибровке
uint8_t Check_Motor_Ready_For_Calibration(uint8_t motor_id);

// Восстановление исходных настроек
void Restore_Original_Settings(uint8_t motor_id);

// Применение результатов калибровки
void Apply_Calibration_Results(uint8_t motor_id, motor_calibration_result_t* result);

// Валидация результатов калибровки
uint8_t Validate_Calibration_Results(motor_calibration_result_t* result);

#endif // MOTOR_AUTOCALIBRATION_H
