# 📚 CORDON-82 ПОЛНАЯ ДОКУМЕНТАЦИЯ СИСТЕМЫ
## Руководство пользователя и техническая документация

**Версия системы:** 2.0  
**Дата создания:** 14.06.2025  
**Статус:** Производственная версия  

---

## 📋 **СОДЕРЖАНИЕ**

1. [Обзор системы](#обзор-системы)
2. [Архитектура системы](#архитектура-системы)
3. [Команды управления](#команды-управления)
4. [Технические характеристики](#технические-характеристики)
5. [Руководство по эксплуатации](#руководство-по-эксплуатации)
6. [Диагностика и обслуживание](#диагностика-и-обслуживание)
7. [Безопасность](#безопасность)
8. [Устранение неисправностей](#устранение-неисправностей)

---

## 🎯 **ОБЗОР СИСТЕМЫ**

### **Назначение**
CORDON-82 - автоматизированная система управления многоосевым механизмом с 7 шаговыми моторами и 1 DC мотором для точного позиционирования и выполнения технологических операций.

### **Основные возможности**
- ✅ Управление 7 шаговыми моторами (M1-M6) и 1 DC мотором (M7)
- ✅ Автоматическая калибровка и оптимизация скоростей
- ✅ Система мониторинга и диагностики в реальном времени
- ✅ Предиктивное обслуживание с прогнозированием отказов
- ✅ Централизованная система отчетности
- ✅ Безопасные режимы работы с контролем датчиков

### **Ключевые улучшения версии 2.0**
- 🚀 **Увеличение производительности на 27%**
- 🛡️ **Устранение критических ошибок безопасности**
- 🤖 **Автоматическая калибровка моторов**
- 📊 **Система предиктивного обслуживания**
- 📈 **Централизованная отчетность**

---

## 🏗️ **АРХИТЕКТУРА СИСТЕМЫ**

### **Аппаратная конфигурация**
```
CORDON-82 System Architecture:

┌─────────────────────────────────────────────────────────────┐
│                    STM32F103 Controller                     │
├─────────────────────────────────────────────────────────────┤
│  M1: Горизонтальная наводка  │  M5: Механизм загрузки      │
│  M2: Вертикальная наводка    │  M6: Барабан (6 позиций)    │
│  M3: Каретка подъема         │  M7: Захват (DC мотор)      │
│  M4: Продольное перемещение  │                             │
├─────────────────────────────────────────────────────────────┤
│  Датчики: D1-D14 (концевые выключатели)                    │
│  Энкодеры: ENC1, ENC2 (обратная связь)                     │
│  Интерфейс: LCD 4x20, 6 кнопок управления                  │
└─────────────────────────────────────────────────────────────┘
```

### **Программная архитектура**
```
Software Modules:

┌─────────────────────────────────────────────────────────────┐
│                      Main Controller                        │
├─────────────────────────────────────────────────────────────┤
│  Motor Control      │  Diagnostics       │  Safety Systems  │
│  - Unified Config   │  - Real-time Mon.  │  - Sensor Check  │
│  - Auto Calibration │  - Health Reports  │  - Emergency Stop│
│  - Speed Optimization│ - Predictive Maint.│ - Error Handling │
├─────────────────────────────────────────────────────────────┤
│  User Interface     │  Data Management   │  Communication   │
│  - LCD Display      │  - Statistics      │  - Command Parser│
│  - Button Input     │  - Logging         │  - Status Reports│
└─────────────────────────────────────────────────────────────┘
```

---

## 🎮 **КОМАНДЫ УПРАВЛЕНИЯ**

### **Базовые команды (7-17)**
| Команда | Функция | Описание |
|---------|---------|----------|
| 7 | READY | Приведение всех моторов в исходное положение |
| 8 | M1 CW | Поворот M1 по часовой стрелке |
| 9 | M1 CCW | Поворот M1 против часовой стрелки |
| 10 | M2 CW | Поворот M2 по часовой стрелке |
| 11 | M2 CCW | Поворот M2 против часовой стрелки |
| 12 | M3 Forward | Движение M3 вперед (подъем) |
| 13 | M3 Back | Движение M3 назад (опускание) |
| 14 | M4 Forward | Движение M4 вперед (выдвижение) |
| 15 | M4 Back | Движение M4 назад (втягивание) |
| 16 | M5 Forward | Движение M5 вперед |
| 17 | M5 Back | Движение M5 назад |

### **Расширенные команды (76-85)**
| Команда | Функция | Время выполнения | Описание |
|---------|---------|------------------|----------|
| 76 | MOTOR REPORT | 10-15 сек | Детальный отчет о состоянии всех моторов |
| 77 | PREDICTIVE ANALYSIS | 2 мин | Предиктивный анализ (временно отключен) |
| 78 | MAINTENANCE FORECAST | 1 мин | Прогноз обслуживания (временно отключен) |
| 79 | HEALTH REPORT | 30 сек | Отчет о здоровье (временно отключен) |
| 80 | CALIBRATE M2 | 2 мин | Автокалибровка мотора M2 |
| 81 | CALIBRATE CRITICAL | 5 мин | Автокалибровка критичных моторов |
| 82 | CALIBRATE ALL | 15 мин | Автокалибровка всех моторов |
| 85 | FAST READY | 44 сек | Ускоренная команда READY (+27% скорости) |

### **Специальные команды**
| Команда | Функция | Описание |
|---------|---------|----------|
| 99 | MAX SPEED TEST | Тест всех моторов на максимальной скорости |

---

## ⚙️ **ТЕХНИЧЕСКИЕ ХАРАКТЕРИСТИКИ**

### **Параметры моторов**
| Мотор | Функция | Частота (Гц) | Улучшение | Редуктор |
|-------|---------|--------------|-----------|----------|
| M1 | Горизонтальная наводка | 500 | +150% | 1:40 |
| M2 | Вертикальная наводка | 100 | +100% | 1:40 |
| M3 | Каретка подъема | 1250 | +150% | 1:1 |
| M4 | Продольное перемещение | 2000 | +300% | 1:1 |
| M5 | Механизм загрузки | 1667 | +233% | 1:1 |
| M6 | Барабан (6 позиций) | 500 | Безопасный режим | 1:1 |
| M7 | Захват (DC мотор) | Переменная | Контроль по датчикам | - |

### **Датчики системы**
| Датчик | Назначение | Тип |
|--------|------------|-----|
| D1 | M3 нижнее положение | Концевой выключатель |
| D2 | M3 верхнее положение | Концевой выключатель |
| D3 | M6 обнаружение снаряда | Оптический датчик |
| D4 | M6 подсчет снарядов | Концевой выключатель |
| D5 | M5 исходное положение | Концевой выключатель |
| D6 | M5 рабочее положение | Концевой выключатель |
| D7 | M4 втянутое положение | Концевой выключатель |
| D8 | M4 выдвинутое положение | Концевой выключатель |
| D10 | M7 захват открыт | Концевой выключатель |
| D11 | M7 захват закрыт | Концевой выключатель |
| D13 | M2 горизонтальное положение | Концевой выключатель |
| D14 | M1 нулевое положение | Концевой выключатель |

### **Производительность системы**
- **Время выполнения READY:** 44 секунды (было 60 секунд)
- **Улучшение производительности:** 27%
- **Размер программы:** 30,664 байт
- **Использование RAM:** 3,116 байт
- **Частота контроллера:** 72 МГц

---

## 📖 **РУКОВОДСТВО ПО ЭКСПЛУАТАЦИИ**

### **Запуск системы**
1. **Подача питания** - включить основное питание системы
2. **Инициализация** - дождаться завершения самодиагностики
3. **Проверка датчиков** - убедиться в корректном состоянии всех датчиков
4. **Выполнение READY** - команда 7 или 85 для приведения в исходное положение

### **Ежедневная эксплуатация**
1. **Утренняя проверка:**
   - Команда 76 (MOTOR REPORT) - проверка состояния моторов
   - Визуальный осмотр механических частей
   - Проверка работы датчиков

2. **Рабочий цикл:**
   - Команда 7 или 85 (READY) - приведение в исходное положение
   - Выполнение рабочих операций командами 8-17
   - Контроль состояния через LCD дисплей

3. **Завершение работы:**
   - Команда 7 (READY) - возврат в безопасное положение
   - Команда 76 (MOTOR REPORT) - финальная проверка
   - Отключение питания

### **Еженедельное обслуживание**
1. **Автокалибровка:**
   - Команда 80 - калибровка M2 (самый нагруженный)
   - Команда 81 - калибровка критичных моторов
   - При необходимости команда 82 - калибровка всех моторов

2. **Диагностика:**
   - Команда 99 - тест максимальных скоростей
   - Анализ отчетов команды 76
   - Проверка механических соединений

---

## 🔧 **ДИАГНОСТИКА И ОБСЛУЖИВАНИЕ**

### **Система мониторинга**
Система автоматически отслеживает:
- **Количество операций** каждого мотора
- **Процент успешных операций**
- **Время выполнения операций**
- **Количество ошибок**
- **Общее время работы**

### **Классификация состояния моторов**
| Состояние | Успешность | Действие |
|-----------|------------|----------|
| EXCELLENT | > 80% | Нормальная работа |
| GOOD | 60-80% | Плановый контроль |
| WARNING | 40-60% | Требует внимания |
| CRITICAL | < 40% | Срочное обслуживание |

### **Автоматические предупреждения**
- **Звуковые сигналы** при критических ошибках
- **Сообщения на LCD** о состоянии системы
- **Автоматическая остановка** при опасных ситуациях

### **Процедуры калибровки**
1. **Автоматическая калибровка (рекомендуется):**
   - Команда 80-82 выполняет полный цикл калибровки
   - Система автоматически находит оптимальные настройки
   - Результаты сохраняются в памяти

2. **Ручная настройка (для экспертов):**
   - Изменение параметров в main.c
   - Тестирование командой 99
   - Проверка стабильности работы

---

## 🛡️ **БЕЗОПАСНОСТЬ**

### **Встроенные системы безопасности**
1. **Контроль датчиков:**
   - Проверка состояния перед каждой операцией
   - Автоматическая остановка при срабатывании концевых выключателей
   - Защита от столкновений

2. **Ограничения движения:**
   - Максимальное количество шагов за операцию
   - Таймауты выполнения операций
   - Контроль скорости движения

3. **Аварийные остановки:**
   - Немедленная остановка при обнаружении препятствий
   - Отключение питания моторов при ошибках
   - Звуковая сигнализация опасных ситуаций

### **Особенности безопасности M6**
- **Специальный режим** с контролем датчика D3
- **Ограничение шагов** (максимум 50 за операцию)
- **Принудительная остановка** при обнаружении снаряда
- **Безопасное завершение** с отключением мотора

### **Рекомендации по безопасности**
1. **Перед началом работы:**
   - Убедиться в отсутствии людей в рабочей зоне
   - Проверить состояние всех датчиков
   - Выполнить команду READY для проверки системы

2. **Во время работы:**
   - Не вмешиваться в работу механизмов
   - Следить за сообщениями на LCD дисплее
   - При появлении ошибок немедленно остановить систему

3. **При обслуживании:**
   - Отключить питание перед механическими работами
   - Использовать только рекомендованные инструменты
   - После обслуживания выполнить полную проверку

---

## 🔍 **УСТРАНЕНИЕ НЕИСПРАВНОСТЕЙ**

### **Частые проблемы и решения**

#### **Проблема: Мотор не реагирует на команды**
**Симптомы:** Отсутствие движения, нет звука работы мотора
**Решения:**
1. Проверить подключение питания
2. Выполнить команду 76 для диагностики
3. Проверить состояние драйвера мотора
4. Выполнить автокалибровку (команды 80-82)

#### **Проблема: Мотор движется рывками**
**Симптомы:** Неравномерное движение, вибрации
**Решения:**
1. Выполнить автокалибровку конкретного мотора
2. Проверить механические соединения
3. Снизить скорость через настройки
4. Проверить питание системы

#### **Проблема: Ошибки датчиков**
**Симптомы:** Сообщения об ошибках датчиков на LCD
**Решения:**
1. Очистить датчики от загрязнений
2. Проверить подключение проводов
3. Выполнить команду READY для сброса
4. При необходимости заменить датчик

#### **Проблема: M6 зависает**
**Симптомы:** Остановка M6 без достижения датчика
**Решения:**
1. Система автоматически остановит мотор (новая функция безопасности)
2. Проверить состояние датчика D3
3. Очистить путь движения от препятствий
4. Выполнить ручную проверку механизма

#### **Проблема: Низкая производительность**
**Симптомы:** Медленное выполнение операций
**Решения:**
1. Использовать команду 85 (FAST READY) вместо 7
2. Выполнить автокалибровку всех моторов (команда 82)
3. Проверить отчет команды 76 на наличие проблемных моторов
4. Очистить механизмы от загрязнений

### **Коды ошибок**
| Код | Описание | Действие |
|-----|----------|----------|
| E01 | Таймаут мотора | Проверить механику, выполнить калибровку |
| E02 | Ошибка датчика | Проверить подключение, очистить датчик |
| E03 | Превышение лимита шагов | Проверить препятствия, сбросить систему |
| E04 | Ошибка драйвера | Проверить питание, перезапустить систему |
| E05 | Критическое состояние мотора | Выполнить обслуживание, заменить компоненты |

### **Контакты технической поддержки**
- **Техническая документация:** Documentation/
- **Логи системы:** Команда 76 (MOTOR REPORT)
- **Диагностика:** Команды 77-79 (при активации)
- **Автокалибровка:** Команды 80-82

---

**Документация подготовлена:** Инженерной службой CORDON-82  
**Версия:** 2.0  
**Дата:** 14.06.2025  
**Статус:** Производственная версия
